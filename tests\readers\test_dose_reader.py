import os
import pytest
import numpy as np
from pinnacle_io.services.file_reader import <PERSON><PERSON>eader
from pinnacle_io.readers.institution_reader import InstitutionReader
from pinnacle_io.readers.patient_reader import PatientReader
from pinnacle_io.readers.trial_reader import TrialReader
from pinnacle_io.readers.dose_reader import DoseReader

"""
Unit tests for Dose<PERSON>eader using real Pinnacle test data from tests/test_data/01.
This test loads a patient and trial, then reads the dose using DoseReader.read.
"""

test_data_dir = os.path.join(os.path.dirname(__file__), "..", "test_data", "01", "Institution_1", "Mount_0", "Patient_1", "Plan_0")


@pytest.mark.skipif(not os.path.exists(test_data_dir), reason="Test data directory not found.")
def test_dose_reader_plan_trial_binary():
    # Set up file service for the plan directory
    file_service = FileReader(test_data_dir)

    # Load trial object (plan.Trial)
    trial_path = "plan.Trial"
    trial_file = os.path.join(test_data_dir, trial_path)
    assert os.path.exists(trial_file), f"Missing {trial_file}"
    with open(trial_file, "r") as f:
        trials = TrialReader.parse(f.readlines())
    assert trials, "No trials found in plan.Trial"
    trial = trials[0]

    # DoseReader.read expects a populated trial (with beams and dose grid)
    # The test data should have at least one beam with a dose_volume_file
    assert trial.beam_list, "Trial has no beams"
    assert trial.dose_grid is not None, "Trial has no dose grid"

    # Read the dose using the service abstraction
    dose = DoseReader.read(file_service, test_data_dir, trial)
    assert dose is not None, "DoseReader.read returned None"
    assert hasattr(dose, "pixel_data"), "Dose object missing pixel_data"
    assert isinstance(dose.pixel_data, np.ndarray), "Dose pixel_data is not a numpy array"
    # Check shape matches dose grid
    shape = (trial.dose_grid.dimension.z, trial.dose_grid.dimension.y, trial.dose_grid.dimension.x)
    assert dose.pixel_data.shape == shape, f"Dose pixel_data shape {dose.pixel_data.shape} != expected {shape}"
    # Check dtype
    assert dose.pixel_data.dtype == np.float32, f"Dose pixel_data dtype {dose.pixel_data.dtype} != np.float32"

    # Check the max dose values. Note that the dose reader has to load the dose for each
    # individual beam to generate the full trial dose.
    assert abs(dose.get_max_dose() - 3211) < 1, f"Trial max dose {dose.get_max_dose()} != 3211"
    assert abs(trial.beam_list[0].dose.get_max_dose() - 2023) < 1, f"Beam 1 max dose {trial.beam_list[0].dose.get_max_dose()} != 2023"
    assert abs(trial.beam_list[1].dose.get_max_dose() - 2082) < 1, f"Beam 1 max dose {trial.beam_list[1].dose.get_max_dose()} != 2082"

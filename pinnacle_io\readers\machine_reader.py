"""
Reader for Pinnacle plan.Pinnacle.Machines files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Machine
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class MachineReader:
    """
    Reader for Pinnacle plan.Pinnacle.Machines files.
    """

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> List[Machine]:
        """
        Read a Pinnacle plan.Pinnacle.Machines directory and create a list of Machine models.

        Args:
            plan_path: Path to the plan directory or plan.Pinnacle.Machines file
            file_service: File service object with open_file method

        Returns:
            List of Machine models populated with data from the files
        """
        if plan_path.lower().endswith("plan.pinnacle.machines"):
            file_path, file_name = os.path.split(plan_path)
        else:
            file_path, file_name = plan_path, "plan.Pinnacle.Machines"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"Machine file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return MachineReader.parse(f.readlines())
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Machine file not found: {full_path}")
        
        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return MachineReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> List[Machine]:
        """
        Parse a Pinnacle Machine content string and create a list of Machine models.

        Args:
            content_lines: Pinnacle Machine content lines

        Returns:
            List of Machine models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        machines: List[Machine] = []
        for machine_data in data.values():
            machines.append(Machine(**machine_data))
        return machines

from abc import ABC, abstractmethod
from typing import Any, List, IO
from pinnacle_io.readers.institution_reader import <PERSON><PERSON><PERSON><PERSON>
from pinnacle_io.readers.patient_reader import <PERSON><PERSON><PERSON>eader
from pinnacle_io.readers.image_set_reader import <PERSON><PERSON>etReader
from pinnacle_io.readers.image_info_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>eader
from pinnacle_io.readers.trial_reader import <PERSON><PERSON>eader
from pinnacle_io.readers.roi_reader import <PERSON><PERSON><PERSON><PERSON>er
from pinnacle_io.readers.point_reader import <PERSON><PERSON><PERSON>er
from pinnacle_io.readers.patient_setup_reader import Patient<PERSON>etupReader
from pinnacle_io.readers.machine_reader import MachineReader
from pinnacle_io.readers.dose_reader import DoseReader


class BaseReaderService(ABC):
    """
    Abstract base class for unified Pinnacle IO services.
    Provides a uniform interface for accessing Pinnacle data objects from various sources (filesystem, tar, zip, memory, etc).
    """

    @abstractmethod
    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file-like object for the given filepath and filename from the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode (e.g., 'r', 'rb').
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the source.
        """
        pass

    @abstractmethod
    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        pass

    # All get_* methods below use 'with self.open_file(..., "r") as f: ... f.readlines()'.
    # This works for both regular file objects and io.TextIOWrapper (as used in ZipFileReader),
    # so these methods are compatible with all service implementations.

    def get_institution(self) -> Any:
        """
        Retrieve an Institution object from the source.
        """
        with self.open_file("", "Institution", "r") as f:
            return InstitutionReader.parse(f.readlines())

    def get_patient(self) -> Any:
        """
        Retrieve a Patient object from the source.
        """
        with self.open_file("", "Patient", "r") as f:
            return PatientReader.parse(f.readlines())

    def get_image_set(self, index: int = 0) -> Any:
        """
        Retrieve an ImageSet object from the source.
        Args:
            index: Index of the ImageSet to retrieve (default: 0).
        """
        header_name = f"ImageSet_{index}.header"
        with self.open_file("", header_name, "r") as f:
            return ImageSetReader.parse(f.readlines())

    def get_image_info(self, index: int = 0) -> Any:
        """
        Retrieve an ImageInfo object from the source.
        Args:
            index: Index of the ImageInfo to retrieve (default: 0).
        """
        info_name = f"ImageSet_{index}.ImageInfo"
        with self.open_file("", info_name, "r") as f:
            return ImageInfoReader.parse(f.readlines())

    def get_trials(self) -> List[Any]:
        """
        Retrieve a list of Trial objects from the source.
        """
        with self.open_file("", "plan.Trial", "r") as f:
            return TrialReader.parse(f.readlines())

    def get_rois(self) -> List[Any]:
        """
        Retrieve a list of ROI objects from the source.
        """
        with self.open_file("", "plan.roi", "r") as f:
            return ROIReader.parse(f.readlines())

    def get_points(self) -> List[Any]:
        """
        Retrieve a list of Point objects from the source.
        """
        with self.open_file("", "plan.Points", "r") as f:
            return PointReader.parse(f.readlines())

    def get_patient_setup(self) -> Any:
        """
        Retrieve a PatientSetup object from the source.
        """
        with self.open_file("", "plan.PatientSetup", "r") as f:
            return PatientSetupReader.parse(f.readlines())

    def get_machines(self) -> List[Any]:
        """
        Retrieve a list of Machine objects from the source.
        """
        with self.open_file("", "plan.Pinnacle.Machines", "r") as f:
            return MachineReader.parse(f.readlines())

    def get_dose(self, plan_path: str, trial: Any = None) -> Any:
        """
        Retrieve a Dose object from the source.
        Args:
            plan_path: Relative path to the plan directory within the file service
            trial: Optionally specify a Trial object for which to retrieve the dose.
        Returns:
            Dose object using the file service abstraction.
        """
        return DoseReader.read(self, plan_path, trial)

import pytest
import zipfile
import tempfile
import os
from pathlib import Path
from pinnacle_io.services.zip_file_reader import ZipFileReader


class TestZipFileReader:
    """Test cases for ZipFileReader service."""

    @pytest.fixture
    def test_zip_path(self):
        """Get path to test zip file."""
        test_dir = Path(__file__).parent.parent
        return test_dir / "test_data" / "01.zip"

    @pytest.fixture
    def zip_reader(self, test_zip_path):
        """Create ZipFileReader instance with test data."""
        if test_zip_path.exists():
            return ZipFileReader(str(test_zip_path))
        else:
            pytest.skip(f"Test zip file not found: {test_zip_path}")

    @pytest.fixture
    def temp_zip_file(self):
        """Create a temporary zip file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            temp_zip_path = temp_zip.name
        
        reader = None
        try:
            # Create zip file with test content
            with zipfile.ZipFile(temp_zip_path, 'w') as zf:
                # Add test files with different path formats
                # Note: In zip files, backslashes are treated as literal characters, not path separators
                test_files = {
                    'Institution': 'Name = "Test Institution";\nAddress = "123 Test St";\n',
                    'Patient': 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n',
                    'plan.Trial': 'Trial = {\n  Name = "Test Trial";\n};\n',
                    'Institution_1/Mount_0/Patient_1/Patient': 'Name = "Nested Patient";\n',
                    'Institution_1/Mount_0/Patient_1/Plan_0/plan.roi': 'ROI = {\n  Name = "Test ROI";\n};\n',
                    # Test with Linux-style paths (standard in zip files)
                    'linux/style/path.txt': 'Linux style path content\n',
                    # Test with special characters
                    'special-file_name.txt': 'Special filename content\n'
                }
                
                for filename, content in test_files.items():
                    zf.writestr(filename, content)
            
            yield temp_zip_path, test_files
        finally:
            # Ensure zip file is closed before cleanup
            if reader is not None and hasattr(reader, '_zipfile'):
                try:
                    reader._zipfile.close()
                except:
                    pass
            # Clean up with retry logic for Windows
            self._cleanup_temp_file(temp_zip_path)
    
    def _cleanup_temp_file(self, temp_path):
        """Helper method to clean up temporary files with retry logic for Windows."""
        import time
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(0.1)  # Brief wait before retry
                else:
                    # If we can't delete, just log it - don't fail the test
                    pass

    def test_init(self, temp_zip_file):
        """Test ZipFileReader initialization."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        assert reader.zip_path == temp_zip_path
        assert reader._zipfile is not None

    def test_open_file_existing(self, temp_zip_file):
        """Test opening existing files from zip."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']

    def test_open_file_binary_mode(self, temp_zip_file):
        """Test opening files in binary mode."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read().replace(b'\r\n', b'\n')
            assert content == test_files['Institution'].encode()

    def test_open_file_not_found(self, temp_zip_file):
        """Test FileNotFoundError when file doesn't exist in zip."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'nonexistent_file.txt', 'r')
        
        assert "nonexistent_file.txt not found in zip archive" in str(exc_info.value)

    def test_open_file_nested_path(self, temp_zip_file):
        """Test opening files in nested directories within zip."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        nested_file = 'Institution_1/Mount_0/Patient_1/Patient'
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files[nested_file]

    def test_exists_file_found(self, temp_zip_file):
        """Test exists method returns True for existing files."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        assert reader.exists('', 'Institution') is True
        assert reader.exists('', 'Patient') is True
        assert reader.exists('', 'plan.Trial') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True

    def test_exists_file_not_found(self, temp_zip_file):
        """Test exists method returns False for non-existing files."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        assert reader.exists('', 'nonexistent_file.txt') is False
        assert reader.exists('Institution_1/Mount_0', 'nonexistent.txt') is False

    def test_linux_path_format_in_zip(self, temp_zip_file):
        """Test with Linux-style paths in zip files."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        linux_path = 'linux/style/path.txt'
        assert reader.exists(linux_path) is True
        
        with reader.open_file('linux/style', 'path.txt', 'r') as f:
            content = f.read()
            assert content == test_files[linux_path]

    def test_windows_path_format_in_zip(self, temp_zip_file):
        """Test understanding of Windows-style paths in zip files."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Note: In zip files, backslashes are NOT treated as path separators
        # They are literal characters in the filename
        # So 'windows\\style\\path.txt' would be a single filename with backslashes
        # This test verifies that we understand this behavior
        windows_style_literal = 'windows\\style\\path.txt'
        # This should NOT exist because we didn't create it with literal backslashes
        assert reader.exists(windows_style_literal) is False
        
        # Standard forward slash paths work in zip files
        linux_style_path = 'linux/style/path.txt'
        assert reader.exists(linux_style_path) is True
        
        with reader.open_file('linux/style', 'path.txt', 'r') as f:
            content = f.read()
            assert content == test_files[linux_style_path]

    def test_cross_platform_path_handling(self, temp_zip_file):
        """Test that zip files handle path formats correctly."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Standard forward slash paths work in zip files
        assert reader.exists('linux/style/path.txt') is True
        
        # Backslash paths are treated as literal filenames, not path separators
        # So this should NOT exist unless we explicitly created it
        assert reader.exists('windows\\style\\path.txt') is False
        
        # Test that we can read the existing path
        with reader.open_file('linux/style/path.txt', mode='r') as f:
            content = f.read()
            assert content == test_files['linux/style/path.txt']
        
        # Test nested paths with forward slashes work correctly
        nested_path = 'Institution_1/Mount_0/Patient_1/Patient'
        assert reader.exists(nested_path) is True
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files[nested_path]

    def test_special_filenames(self, temp_zip_file):
        """Test files with special characters in names."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        special_file = 'special-file_name.txt'
        assert reader.exists('', special_file) is True
        
        with reader.open_file('', special_file, 'r') as f:
            content = f.read()
            assert content == test_files[special_file]

    def test_integration_with_real_test_data(self, zip_reader):
        """Test with actual test zip file if available."""
        if zip_reader is None:
            pytest.skip("Real test zip file not available")
        
        # Test that we can check for existence of files that might be in the real zip
        potential_files = [
            '01/Institution',
            '01/Institution_1/Mount_0/Patient_1/Patient',
            '01/Institution_1/Mount_0/Patient_1/ImageSet_0.header',
            '01/Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial'
        ]
        
        for filename in potential_files:
            if zip_reader.exists(filename):
                # If file exists, we should be able to open it
                with zip_reader.open_file(filename, mode='r') as f:
                    content = f.read()
                    assert isinstance(content, str)
                    assert len(content) > 0

    def test_error_message_includes_zip_path(self, temp_zip_file):
        """Test that error messages include helpful zip path information."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'missing_file.txt', 'r')
        
        error_msg = str(exc_info.value)
        assert 'missing_file.txt' in error_msg
        assert 'zip archive' in error_msg

    def test_file_modes(self, temp_zip_file):
        """Test different file opening modes."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Test text mode
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert isinstance(content, str)
        
        # Test binary mode
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read()
            assert isinstance(content, bytes)

    def test_text_wrapper_encoding(self, temp_zip_file):
        """Test that text mode uses latin1 encoding as specified."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            # Check that we have a TextIOWrapper
            assert hasattr(f, 'encoding')
            # The encoding should be latin1 as specified in the implementation
            assert f.encoding == 'latin1'

    def test_zip_file_cleanup(self, temp_zip_file):
        """Test that zip file resources are properly managed."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Access the internal zip file
        zip_file = reader._zipfile
        assert zip_file is not None
        
        # Use the reader
        assert reader.exists('', 'Institution') is True
        
        # The zip file should still be accessible
        assert zip_file.fp is not None

    def test_nonexistent_zip_file(self):
        """Test behavior when zip file doesn't exist."""
        with pytest.raises(FileNotFoundError):
            ZipFileReader('/nonexistent/path/to/file.zip')

    def test_invalid_zip_file(self):
        """Test behavior with invalid zip file."""
        # Create a non-zip file
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_file:
            temp_file.write(b'This is not a zip file')
            temp_path = temp_file.name
        
        try:
            with pytest.raises(zipfile.BadZipFile):
                ZipFileReader(temp_path)
        finally:
            os.unlink(temp_path)

    def test_empty_zip_file(self):
        """Test behavior with empty zip file."""
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            temp_zip_path = temp_zip.name
        
        reader = None
        try:
            # Create empty zip file
            with zipfile.ZipFile(temp_zip_path, 'w') as zf:
                pass  # Empty zip
            
            reader = ZipFileReader(temp_zip_path)
            
            # Should work but have no files
            assert reader.exists('any_file.txt') is False
            
            with pytest.raises(FileNotFoundError):
                reader.open_file('', 'any_file.txt', 'r')
        finally:
            # Ensure zip file is closed before cleanup
            if reader is not None and hasattr(reader, '_zipfile'):
                try:
                    reader._zipfile.close()
                except:
                    pass
            self._cleanup_temp_file(temp_zip_path)

    def test_zip_with_directories(self, temp_zip_file):
        """Test that zip reader handles directory entries correctly."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Check that nested file paths work
        nested_file = 'Institution_1/Mount_0/Patient_1/Patient'
        assert reader.exists(nested_file) is True
        
        # Directory entries themselves might not be explicitly stored in zip
        # but file paths should work
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert 'Nested Patient' in content

    def test_case_sensitivity(self, temp_zip_file):
        """Test case sensitivity in zip file names."""
        temp_zip_path, _ = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Zip file names are typically case-sensitive
        assert reader.exists('', 'Institution') is True
        assert reader.exists('', 'institution') is False  # Different case
        assert reader.exists('', 'INSTITUTION') is False  # Different case

    def test_new_api_filepath_filename(self, temp_zip_file):
        """Test the new API with separate filepath and filename parameters."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Test with separate filepath and filename
        assert reader.exists('', 'Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_new_api_full_path(self, temp_zip_file):
        """Test the new API with full path as filepath parameter."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Test with full path as filepath (filename=None)
        assert reader.exists('Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Patient') is True
        
        with reader.open_file('Institution', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Patient', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_pattern_matching(self, temp_zip_file):
        """Test that the zip reader can find files using pattern matching."""
        temp_zip_path, test_files = temp_zip_file
        reader = ZipFileReader(temp_zip_path)
        
        # Test pattern matching - should find files that start with filepath and end with filename
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi') is True
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Plan_0/plan.roi']

    def test_multiple_matches_error(self, temp_zip_file):
        """Test that multiple matches result in an error."""
        temp_zip_path, test_files = temp_zip_file
        
        # Create a zip file with multiple files that could match the same pattern
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            temp_zip_path2 = temp_zip.name
        
        try:
            with zipfile.ZipFile(temp_zip_path2, 'w') as zf:
                # Add files that would match the same pattern
                zf.writestr('dir1/test.txt', 'content1')
                zf.writestr('dir2/test.txt', 'content2')
            
            reader = ZipFileReader(temp_zip_path2)
            
            # This should raise an error due to multiple matches
            with pytest.raises(FileNotFoundError) as exc_info:
                reader.open_file('', 'test.txt', 'r')
            
            assert 'Multiple files match' in str(exc_info.value)
        finally:
            self._cleanup_temp_file(temp_zip_path2)
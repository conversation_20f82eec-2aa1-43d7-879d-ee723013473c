from .base_reader_service import BaseReaderService
from typing import Any, IO
import zipfile


class ZipFileReader(BaseReaderService):
    """
    ZipFileReader service for reading Pinnacle data from a zip archive.
    Implements open_file using Python's zipfile.ZipFile.
    All get_* methods are inherited from BaseReaderService.
    """

    def __init__(self, zip_path: str):
        self.zip_path = zip_path
        self._zipfile = zipfile.ZipFile(zip_path, "r")

    def _find_file(self, filepath: str, filename: str | None = None) -> str:
        """
        Find a file in the zip archive based on filepath and filename.
        Args:
            filepath: Directory path or full file path within the zip archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            The filename/path of the found file within the zip archive.
        Raises:
            FileNotFoundError: If the specified file does not exist in the zip archive.
        """
        # Build the full path to search for
        if filename is None:
            target_path = filepath
        else:
            target_path = f"{filepath}/{filename}" if filepath else filename
        
        # First try exact match
        try:
            zipinfo = self._zipfile.getinfo(target_path)
            return zipinfo.filename
        except KeyError:
            # If exact match fails, search for members that match the pattern
            matching_members: list[str] = []
            for zipinfo in self._zipfile.infolist():
                if filename is None:
                    # Looking for exact filepath match
                    if zipinfo.filename == filepath:
                        matching_members.append(zipinfo.filename)
                else:
                    # Looking for members that start with filepath and end with filename
                    if filepath:
                        if zipinfo.filename.startswith(f"{filepath}/") and zipinfo.filename.endswith(filename):
                            matching_members.append(zipinfo.filename)
                    else:
                        if zipinfo.filename.endswith(filename):
                            matching_members.append(zipinfo.filename)
            
            if not matching_members:
                display_path = f"{filepath}/{filename}" if filename else filepath
                raise FileNotFoundError(f"File {display_path} not found in zip archive {self.zip_path}")
            
            if len(matching_members) > 1:
                display_path = f"{filepath}/{filename}" if filename else filepath
                raise FileNotFoundError(f"Multiple files match {display_path} in zip archive {self.zip_path}")
            
            return matching_members[0]

    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file from the zip archive.
        Args:
            filepath: Directory path or full file path within the zip archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode ('r' for text, 'rb' for binary).
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the zip archive.
        """
        target_name = self._find_file(filepath, filename)
        
        try:
            # zipfile only supports binary mode, so we must handle text mode ourselves
            if "b" in mode:
                return self._zipfile.open(target_name, "r")
            else:
                # Open as binary, then wrap in TextIOWrapper for text mode
                import io

                raw = self._zipfile.open(target_name, "r")
                return io.TextIOWrapper(raw, encoding="latin1")
        except KeyError:
            display_path = f"{filepath}/{filename}" if filename else filepath
            raise FileNotFoundError(f"File {display_path} not found in zip archive {self.zip_path}")

    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the zip archive.
        Args:
            filepath: Directory path or full file path within the zip archive.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        try:
            self._find_file(filepath, filename)
            return True
        except FileNotFoundError:
            return False

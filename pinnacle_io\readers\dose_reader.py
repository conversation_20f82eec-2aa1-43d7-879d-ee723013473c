"""
Reader for Pinnacle plan.Trail.binary.### files.
"""

from pinnacle_io.models import Dose, DoseGrid, Trial, Beam, Point
import numpy as np
import numpy.typing as npt
from typing import Any, IO
import os


class DoseReader:
    """
    Reader for Pinnacle plan.Trail.binary.### files.
    Now supports any file service with an open_file(filename, mode) method.
    """

    @staticmethod
    def read(
        file_service: Any,
        plan_path: str,
        trial: Trial,
        points: "list[Point] | None" = None,
    ) -> Dose:
        """
        Read Pinnacle plan.Trail.binary.### files and create a Dose model for the given trial.
        This method saves the dose for each beam as well.

        Args:
            file_service: An object with open_file(filename, mode) for file access (e.g., BaseReaderService)
            plan_path: Relative path to the plan directory within the file service
            trial: Trial model to use for the dose
            points: Optional list of Point objects (if not provided, will be read)

        Returns:
            Trial dose model populated with data from the file. Pixel data is the total trial dose.
        """
        if not trial.beam_list:
            raise ValueError("Trial has no beams")

        dose_grid = trial.dose_grid
        if dose_grid is None:
            raise ValueError("Trial has no dose grid")

        # Read points if not provided
        if points is None:
            from pinnacle_io.readers.point_reader import PointReader

            points = PointReader.read(plan_path)

        dose_dimension = dose_grid.dimension
        if dose_dimension is None or dose_dimension.z is None or dose_dimension.y is None or dose_dimension.x is None:
            raise ValueError("Dose grid has no dimension")

        trial_dose = Dose(
            dose_type="PHYSICAL",
            dose_unit="CGY",
            dose_summation_type="PLAN",
            dose_grid=dose_grid,
            dose_grid_id=dose_grid.id if dose_grid else None,
            x_dim=dose_grid.dimension.x if dose_grid and dose_grid.dimension and dose_grid.dimension.x is not None else 0,
            y_dim=dose_grid.dimension.y if dose_grid and dose_grid.dimension and dose_grid.dimension.y is not None else 0,
            z_dim=dose_grid.dimension.z if dose_grid and dose_grid.dimension and dose_grid.dimension.z is not None else 0,
            x_pixdim=dose_grid.voxel_size.x if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.x is not None else 0,
            y_pixdim=dose_grid.voxel_size.y if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.y is not None else 0,
            z_pixdim=dose_grid.voxel_size.z if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.z is not None else 0,
            x_start=dose_grid.origin.x if dose_grid and dose_grid.origin and dose_grid.origin.x is not None else 0,
            y_start=dose_grid.origin.y if dose_grid and dose_grid.origin and dose_grid.origin.y is not None else 0,
            z_start=dose_grid.origin.z if dose_grid and dose_grid.origin and dose_grid.origin.z is not None else 0,
            dose_comment=trial.name,
            pixel_data=np.zeros((dose_dimension.z, dose_dimension.y, dose_dimension.x), dtype=np.float32),
            trial=trial,
        )
        for beam in trial.beam_list:
            prescription = next((p for p in trial.prescription_list if p.name == beam.prescription_name), None)
            if prescription is None:
                raise ValueError(f"Prescription not found for beam '{beam.name}'")
            beam_dose = DoseReader.read_beam_dose(file_service, plan_path, beam, trial.dose_grid, points)
            if beam_dose.pixel_data is None:
                raise ValueError(f"Beam '{beam.name}' dose has no pixel data")
            beam_dose.pixel_data = beam_dose.pixel_data * prescription.number_of_fractions  # type: ignore
            beam.dose = beam_dose
            trial_dose.pixel_data += beam_dose.pixel_data  # type: ignore

        # Link the dose to the trial. Then return the trial dose.
        trial.dose = trial_dose
        return trial_dose

    @staticmethod
    def read_beam_dose(
        file_service: Any,
        plan_path: str,
        beam: Beam,
        dose_grid: DoseGrid,
        points: "list[Point] | None" = None,
    ) -> Dose:
        """
        Read a beam dose from a Pinnacle binary dose file using the file service.

        Args:
            file_service: An object with open_file(filename, mode) for file access
            plan_path: Relative path to the plan directory within the file service
            beam: Beam model to use for the dose
            dose_grid: DoseGrid model to use for the dose
            points: Optional list of Point objects (if not provided, will be read)

        Returns:
            Dose model populated with data from the file. Pixel data is the dose per fraction.
        """
        # Read points if not provided
        if points is None:
            from pinnacle_io.readers.point_reader import PointReader

            points = PointReader.read(plan_path)

        with file_service.open_file(plan_path, beam.dose_volume_file, "rb") as f:
            dose_per_fx_per_mu = DoseReader.read_binary_dose(f, dose_grid)

        mu_info = beam.monitor_unit_info
        if mu_info is None:
            raise ValueError(f"Beam '{beam.name}' has no monitor unit info")

        beam_dose = Dose(
            dose_type="PHYSICAL",
            dose_unit="CGY",
            dose_summation_type="BEAM",
            dose_grid=dose_grid,
            dose_grid_id=dose_grid.id if dose_grid else None,
            x_dim=dose_grid.dimension.x if dose_grid and dose_grid.dimension and dose_grid.dimension.x is not None else 0,
            y_dim=dose_grid.dimension.y if dose_grid and dose_grid.dimension and dose_grid.dimension.y is not None else 0,
            z_dim=dose_grid.dimension.z if dose_grid and dose_grid.dimension and dose_grid.dimension.z is not None else 0,
            x_pixdim=dose_grid.voxel_size.x if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.x is not None else 0,
            y_pixdim=dose_grid.voxel_size.y if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.y is not None else 0,
            z_pixdim=dose_grid.voxel_size.z if dose_grid and dose_grid.voxel_size and dose_grid.voxel_size.z is not None else 0,
            x_start=dose_grid.origin.x if dose_grid and dose_grid.origin and dose_grid.origin.x is not None else 0,
            y_start=dose_grid.origin.y if dose_grid and dose_grid.origin and dose_grid.origin.y is not None else 0,
            z_start=dose_grid.origin.z if dose_grid and dose_grid.origin and dose_grid.origin.z is not None else 0,
            dose_comment=beam.name,
            pixel_data=dose_per_fx_per_mu,
            beam=beam,
        )

        # Scale the dose by the monitor units
        reference_point = next((p for p in points if getattr(p, "name", None) == beam.prescription_point_name), None)
        if reference_point is None:
            raise ValueError(f"Reference point not found for beam '{beam.name}'")
        reference_dose = beam_dose.get_dose_value_at_point(*reference_point.coordinates)  # type: ignore
        if reference_dose is None:
            raise ValueError(f"Reference dose not found for beam '{beam.name}'")
        monitor_units = mu_info.prescription_dose / reference_dose  # type: ignore
        beam_dose.pixel_data = beam_dose.pixel_data * monitor_units  # type: ignore

        return beam_dose


    @staticmethod
    def read_binary_dose(
        file_obj: IO[bytes],
        dose_grid: DoseGrid | None = None,
    ) -> npt.NDArray[np.float32]:
        """
        Read and parse a Pinnacle binary dose file from a file-like object.

        Args:
            file_obj: File-like object opened in binary mode
            dose_grid: A DoseGrid model object containing the dimensions of the dose volume.

        Returns:
            Numpy array of raw, unscaled dose data (i.e., dose per monitor unit per fraction).
            If the dose grid is provided, the dose data is reshaped to the correct dimensions and flipped along the Y axis.
        """
        binary_data = file_obj.read()
        data_type = ">f4"
        dose_volume = np.frombuffer(binary_data, dtype=data_type)
        if (
            dose_grid
            and dose_grid.dimension
            and dose_grid.dimension.z is not None
            and dose_grid.dimension.y is not None
            and dose_grid.dimension.x is not None
        ):
            z_dim = int(dose_grid.dimension.z)
            y_dim = int(dose_grid.dimension.y)
            x_dim = int(dose_grid.dimension.x)
            dose_volume = dose_volume.reshape((z_dim, y_dim, x_dim))

            # Always flip along the Y axis
            dose_volume = np.flip(dose_volume, axis=1)  # flip y
        return dose_volume
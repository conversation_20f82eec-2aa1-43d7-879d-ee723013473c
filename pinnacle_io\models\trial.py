"""
SQLAlchemy model for Pinnacle Trial data.

This module provides the Trial data model for representing treatment trials in Pinnacle,
including dose settings, beam configurations, and prescription details. A trial in Pinnacle
represents a complete treatment plan configuration and maps to a DICOM RT Plan.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any

from sqlalchemy import Column, String, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.versioned_base import VersionedBase

if TYPE_CHECKING:
    from pinnacle_io.models.beam import Beam
    from pinnacle_io.models.dose import Dose, MaxDosePoint
    from pinnacle_io.models.dose_grid import DoseGrid
    from pinnacle_io.models.plan import Plan
    from pinnacle_io.models.patient_representation import PatientRepresentation
    from pinnacle_io.models.prescription import Prescription


class Trial(VersionedBase):
    """
    Model representing a complete treatment trial (plan) in Pinnacle.

    A Trial in Pinnacle is equivalent to a treatment plan in DICOM terminology. It contains
    all the necessary information for planning and delivering radiation therapy treatment,
    including beam configurations, dose calculations, and prescription details.

    The Trial model serves as the central hub for treatment planning, connecting various
    components such as beams, prescriptions, and dose calculations. It supports both
    conventional external beam radiation therapy and advanced treatment techniques.

    Key Concepts:
        - A Trial belongs to a Plan, which can contain multiple trials for treatment comparison
        - Each Trial can have multiple beams, prescriptions, and associated dose calculations
        - Trials store dose grid settings for accurate dose computation
        - Treatment position and setup information is maintained for patient alignment
        - Quality assurance and treatment delivery parameters are included

    Technical Details:
        - Inherits from VersionedBase for change tracking
        - Uses SQLAlchemy relationships with appropriate cascade behaviors
        - Supports both physical and dynamic wedges
        - Handles IMRT, VMAT, and conventional delivery techniques

    Attributes:
        id (int): Primary key (inherited from VersionedBase)
        trial_id (int): Unique identifier for the trial
        name (str): Name of the trial for display
        dose_start_slice (int): Starting slice number for dose calculation
        dose_end_slice (int): Ending slice number for dose calculation
        scenario_dose_grid_res (float): Resolution of the dose grid in cm
        scenario_dose_grid_dim_x (int): Dose grid dimension in X direction
        scenario_dose_grid_dim_y (int): Dose grid dimension in Y direction
        scenario_dose_grid_dim_z (int): Dose grid dimension in Z direction
        suppress_dose_grid_summing (int): Flag to suppress dose grid summing
        trial_used_for_dose_display_only (int): Flag to indicate trial used for dose display only
        record_dose_data (str): Method of dose data recording
        fluence_grid_resolution (float): Resolution of the fluence grid
        fluence_grid_matches_dose_grid (int): Flag to indicate if fluence grid matches dose grid
        source_to_film_distance (float): Distance from source to film
        screen_to_printer_zoom_factor (float): Zoom factor for screen to printer
        mc_max_seconds (int): Maximum seconds for Monte Carlo calculation
        mc_global_uncertainty_type (str): Type of global uncertainty for Monte Carlo
        mc_statistics_threshold (int): Statistics threshold for Monte Carlo
        mc_uncertainty_goal (int): Uncertainty goal for Monte Carlo
        remove_couch_from_scan (int): Flag to remove couch from scan
        couch_removal_y_coordinate (float): Y coordinate for couch removal
        display_2d_couch_position (int): 2D couch position for display
        display_3d_couch_position (int): 3D couch position for display
        couch_display_color (str): Color of the couch for display
        recompute_density (int): Flag to recompute density
        physics_plan (int): Physics plan identifier
        compute_relative_dose (int): Flag to compute relative dose
        relative_dose_norm_point_name (str): Name of the normalization point for relative dose
        relative_dose_norm_value (float): Value of the normalization point for relative dose
        relative_dose_norm_valid (int): Flag to indicate if relative dose norm is valid
        relative_dose_reference_field_name (str): Name of the reference field for relative dose
        last_relative_dose_reference_field (str): Last reference field used for relative dose
        relative_dose_computation_status (str): Status of the relative dose computation
        isodose_norm_point_name (str): Name of the normalization point for isodose
        use_actual_patient_for_irreg (int): Flag to use actual patient for irregular fields
        print_subbeams (int): Flag to print subbeams
        print_pois (int): Flag to print points of interest
        print_rois (int): Flag to print regions of interest
        print_brachy_by_catheter (int): Flag to print brachytherapy by catheter
        print_brachy_by_group (int): Flag to print brachytherapy by group
        print_mlc (int): Flag to print multi-leaf collimator settings
        print_mlc_irreg (int): Flag to print irregular multi-leaf collimator settings
        use_trial_for_treatment (int): Flag to use trial for treatment
        use_coord_ref_point (int): Flag to use coordinate reference point
        course_id (int): Course identifier
        tolerance_table (int): Tolerance table identifier
        always_display_2d_couch_position (int): Flag to always display 2D couch position
        always_display_3d_couch_position (int): Flag to always display 3D couch position
        export_planar_dose_ascii (int): Flag to export planar dose as ASCII
        print_imrt_summary (int): Flag to print IMRT summary
        print_impt_summary (int): Flag to print IMPT summary
        multiple_machines (int): Flag to indicate use of multiple machines
        check_ct_to_density_extension (int): Flag to check CT to density extension
        series_number (int): Series number for the trial
        series_description (str): Description of the series
        ct_scanner (str): CT scanner used
        has_dose (int): Flag to indicate if dose is present
        include_dicom_coord_in_report (int): Flag to include DICOM coordinates in report
        include_shifts_from_first_in_report (int): Flag to include shifts from first in report
        is_absolute_laser_mode (int): Flag to indicate absolute laser mode
        last_laser_transmission_mode (str): Last laser transmission mode used
        ap_error (int): Anterior-posterior error
        lr_error (int): Left-right error
        is_error (int): General error flag
        depth_error (int): Depth error
        fluence_dose_spread (int): Fluence dose spread
        single_gaussian_dose_spread (int): Single Gaussian dose spread
        double_gaussian_dose_spread (int): Double Gaussian dose spread
        nuclear_dose_spread (int): Nuclear dose spread
        min_dose_threshold (float): Minimum dose threshold
        is_ro (int): Flag to indicate if trial is a RO (Radiation Oncology) trial
        ant_post_weight (int): Anterior-posterior weight
        lateral_weight (int): Lateral weight
        inf_sup_weight (int): Inferior-superior weight
        depth_weight (int): Depth weight
        nominal_weight (int): Nominal weight

    Relationships:
        plan (Plan): Parent plan containing this trial (many-to-one)
        beam_list (list[Beam]): Treatment beams in this trial (one-to-many)
        dose (Dose | None): Associated dose calculation (one-to-one)
        dose_grid (DoseGrid): Dose computation grid settings (one-to-one)
        max_dose_point (MaxDosePoint): Point of maximum dose (one-to-one)
        patient_representation (PatientRepresentation): Patient setup details (one-to-one)
        prescription_list (list[Prescription]): Treatment prescriptions (one-to-many)
    """

    __tablename__: str = "Trial"

    # Primary key is inherited from VersionedBase
    trial_id: Mapped[int | None] = Column("TrialID", Integer, nullable=True)
    name: Mapped[str | None] = Column("TrialName", String, nullable=True)
    dose_start_slice: Mapped[int | None] = Column("DoseStartSlice", Integer, nullable=True)
    dose_end_slice: Mapped[int | None] = Column("DoseEndSlice", Integer, nullable=True)
    scenario_dose_grid_res: Mapped[float | None] = Column("ScenarioDoseGridRes", Float, nullable=True)
    scenario_dose_grid_dim_x: Mapped[int | None] = Column("ScenarioDoseGridDimX", Integer, nullable=True)
    scenario_dose_grid_dim_y: Mapped[int | None] = Column("ScenarioDoseGridDimY", Integer, nullable=True)
    scenario_dose_grid_dim_z: Mapped[int | None] = Column("ScenarioDoseGridDimZ", Integer, nullable=True)
    suppress_dose_grid_summing: Mapped[int | None] = Column("SuppressDoseGridSumming", Integer, nullable=True)
    trial_used_for_dose_display_only: Mapped[int | None] = Column("TrialUsedForDoseDisplayOnly", Integer, nullable=True)
    record_dose_data: Mapped[str | None] = Column("RecordDoseData", String, nullable=True)
    fluence_grid_resolution: Mapped[float | None] = Column("FluenceGridResolution", Float, nullable=True)
    fluence_grid_matches_dose_grid: Mapped[int | None] = Column("FluenceGridMatchesDoseGrid", Integer, nullable=True)
    source_to_film_distance: Mapped[float | None] = Column("SourceToFilmDistance", Float, nullable=True)
    screen_to_printer_zoom_factor: Mapped[float | None] = Column("ScreenToPrinterZoomFactor", Float, nullable=True)
    mc_max_seconds: Mapped[int | None] = Column("MCMaxSeconds", Integer, nullable=True)
    mc_global_uncertainty_type: Mapped[str | None] = Column("MCGlobalUncertaintyType", String, nullable=True)
    mc_statistics_threshold: Mapped[int | None] = Column("MCStatisticsThreshold", Integer, nullable=True)
    mc_uncertainty_goal: Mapped[int | None] = Column("MCUncertaintyGoal", Integer, nullable=True)
    remove_couch_from_scan: Mapped[int | None] = Column("RemoveCouchFromScan", Integer, nullable=True)
    couch_removal_y_coordinate: Mapped[float | None] = Column("CouchRemovalYCoordinate", Float, nullable=True)
    display_2d_couch_position: Mapped[int | None] = Column("Display2DCouchPosition", Integer, nullable=True)
    display_3d_couch_position: Mapped[int | None] = Column("Display3DCouchPosition", Integer, nullable=True)
    couch_display_color: Mapped[str | None] = Column("CouchDisplayColor", String, nullable=True)
    recompute_density: Mapped[int | None] = Column("RecomputeDensity", Integer, nullable=True)
    physics_plan: Mapped[int | None] = Column("PhysicsPlan", Integer, nullable=True)
    compute_relative_dose: Mapped[int | None] = Column("ComputeRelativeDose", Integer, nullable=True)
    relative_dose_norm_point_name: Mapped[str | None] = Column("RelativeDoseNormPointName", String, nullable=True)
    relative_dose_norm_value: Mapped[float | None] = Column("RelativeDoseNormValue", Float, nullable=True)
    relative_dose_norm_valid: Mapped[int | None] = Column("RelativeDoseNormValid", Integer, nullable=True)
    relative_dose_reference_field_name: Mapped[str | None] = Column("RelativeDoseReferenceFieldName", String, nullable=True)
    last_relative_dose_reference_field: Mapped[str | None] = Column("LastRelativeDoseReferenceField", String, nullable=True)
    relative_dose_computation_status: Mapped[str | None] = Column("RelativeDoseComputationStatus", String, nullable=True)
    isodose_norm_point_name: Mapped[str | None] = Column("IsodoseNormPointName", String, nullable=True)
    use_actual_patient_for_irreg: Mapped[int | None] = Column("UseActualPatientForIrreg", Integer, nullable=True)
    print_subbeams: Mapped[int | None] = Column("PrintSubbeams", Integer, nullable=True)
    print_pois: Mapped[int | None] = Column("PrintPOIs", Integer, nullable=True)
    print_rois: Mapped[int | None] = Column("PrintROIs", Integer, nullable=True)
    print_brachy_by_catheter: Mapped[int | None] = Column("PrintBrachyByCatheter", Integer, nullable=True)
    print_brachy_by_group: Mapped[int | None] = Column("PrintBrachyByGroup", Integer, nullable=True)
    print_mlc: Mapped[int | None] = Column("PrintMLC", Integer, nullable=True)
    print_mlc_irreg: Mapped[int | None] = Column("PrintMLCIrreg", Integer, nullable=True)
    use_trial_for_treatment: Mapped[int | None] = Column("UseTrialForTreatment", Integer, nullable=True)
    use_coord_ref_point: Mapped[int | None] = Column("UseCoordRefPoint", Integer, nullable=True)
    course_id: Mapped[int | None] = Column("CourseID", Integer, nullable=True)
    tolerance_table: Mapped[int | None] = Column("ToleranceTable", Integer, nullable=True)
    always_display_2d_couch_position: Mapped[int | None] = Column("AlwaysDisplay2DCouchPosition", Integer, nullable=True)
    always_display_3d_couch_position: Mapped[int | None] = Column("AlwaysDisplay3DCouchPosition", Integer, nullable=True)
    export_planar_dose_ascii: Mapped[int | None] = Column("ExportPlanarDoseASCII", Integer, nullable=True)
    print_imrt_summary: Mapped[int | None] = Column("PrintIMRTSummary", Integer, nullable=True)
    print_impt_summary: Mapped[int | None] = Column("PrintIMPTSummary", Integer, nullable=True)
    multiple_machines: Mapped[int | None] = Column("MultipleMachines", Integer, nullable=True)
    check_ct_to_density_extension: Mapped[int | None] = Column("CheckCTToDensityExtension", Integer, nullable=True)
    series_number: Mapped[int | None] = Column("SeriesNumber", Integer, nullable=True)
    series_description: Mapped[str | None] = Column("SeriesDescription", String, nullable=True)
    ct_scanner: Mapped[str | None] = Column("CTScanner", String, nullable=True)
    has_dose: Mapped[int | None] = Column("HasDose", Integer, nullable=True)
    include_dicom_coord_in_report: Mapped[int | None] = Column("IncludeDicomCoordInReport", Integer, nullable=True)
    include_shifts_from_first_in_report: Mapped[int | None] = Column("IncludeShiftsFromFirstInReport", Integer, nullable=True)
    is_absolute_laser_mode: Mapped[int | None] = Column("IsAbsoluteLaserMode", Integer, nullable=True)
    last_laser_transmission_mode: Mapped[str | None] = Column("LastLaserTransmissionMode", String, nullable=True)
    ap_error: Mapped[int | None] = Column("APError", Integer, nullable=True)
    lr_error: Mapped[int | None] = Column("LRError", Integer, nullable=True)
    is_error: Mapped[int | None] = Column("IsError", Integer, nullable=True)
    depth_error: Mapped[int | None] = Column("DepthError", Integer, nullable=True)
    fluence_dose_spread: Mapped[int | None] = Column("FluenceDoseSpread", Integer, nullable=True)
    single_gaussian_dose_spread: Mapped[int | None] = Column("SingleGaussianDoseSpread", Integer, nullable=True)
    double_gaussian_dose_spread: Mapped[int | None] = Column("DoubleGaussianDoseSpread", Integer, nullable=True)
    nuclear_dose_spread: Mapped[int | None] = Column("NuclearDoseSpread", Integer, nullable=True)
    min_dose_threshold: Mapped[float | None] = Column("MinDoseThreshold", Float, nullable=True)
    is_ro: Mapped[int | None] = Column("IsRO", Integer, nullable=True)
    ant_post_weight: Mapped[int | None] = Column("AntPostWeight", Integer, nullable=True)
    lateral_weight: Mapped[int | None] = Column("LateralWeight", Integer, nullable=True)
    inf_sup_weight: Mapped[int | None] = Column("InfSupWeight", Integer, nullable=True)
    depth_weight: Mapped[int | None] = Column("DepthWeight", Integer, nullable=True)
    nominal_weight: Mapped[int | None] = Column("NominalWeight", Integer, nullable=True)

    # Parent relationship with optimized loading
    plan_id: Mapped[int] = Column("PlanID", Integer, ForeignKey("Plan.ID"), nullable=False)
    plan: Mapped["Plan"] = relationship(
        "Plan",
        back_populates="trial_list",
        lazy="joined",  # Optimize loading as trials are frequently accessed with plans
    )

    # Child relationships with appropriate loading strategies
    beam_list: Mapped[list["Beam"]] = relationship(
        "Beam",
        back_populates="trial",
        cascade="all, delete-orphan",
        lazy="select",  # Default loading as beams might be numerous
    )

    dose: Mapped["Dose | None"] = relationship(
        "Dose",
        back_populates="trial",
        uselist=False,
        cascade="all, delete-orphan",
        foreign_keys="Dose.trial_id",
        lazy="select",  # Load on demand as dose data can be large
    )

    dose_grid: Mapped["DoseGrid | None"] = relationship(
        "DoseGrid",
        uselist=False,
        back_populates="trial",
        cascade="all, delete-orphan",
        lazy="joined",  # Optimize loading as grid settings are frequently needed
    )

    max_dose_point: Mapped["MaxDosePoint | None"] = relationship(
        "MaxDosePoint",
        uselist=False,
        back_populates="trial",
        cascade="all, delete-orphan",
        lazy="joined",  # Small data, frequently needed
    )

    patient_representation: Mapped["PatientRepresentation | None"] = relationship(
        "PatientRepresentation",
        uselist=False,
        back_populates="trial",
        cascade="all, delete-orphan",
        lazy="joined",  # Small data, frequently needed
    )

    prescription_list: Mapped[list["Prescription"]] = relationship(
        "Prescription",
        back_populates="trial",
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as prescriptions might be numerous
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a Trial instance.

        Args:
            **kwargs: Keyword arguments for initializing Trial attributes.
                     See class attributes documentation for available fields.

        Example:
            >>> trial = Trial(
            ...     trial_id=1,
            ...     name="Treatment Plan 1",
            ...     beam_list=[beam1, beam2],
            ...     prescription_list=[prescription]
            ... )
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """
        Return a string representation of this trial.

        Returns:
            str: A string containing the trial's ID, trial_id, and name.
        """
        return f"<Trial(id={self.id}, trial_id={self.trial_id}, name='{self.name}')>"

    def get_beam_by_number(self, beam_number: int) -> "Beam | None":
        """
        Retrieve a beam by its assigned number.

        Args:
            beam_number: The number of the beam to retrieve

        Returns:
            The matching Beam instance if found, None otherwise

        Example:
            >>> trial = Trial(beam_list=[Beam(beam_number=1, name="AP")])
            >>> beam = trial.get_beam_by_number(1)
            >>> print(beam.name)
            'AP'
        """
        for beam in self.beam_list:
            if beam.beam_number == beam_number:
                return beam
        return None

    def get_beam_by_name(self, beam_name: str) -> "Beam | None":
        """
        Retrieve a beam by its name.

        Args:
            beam_name: The name of the beam to retrieve

        Returns:
            The matching Beam instance if found, None otherwise

        Example:
            >>> trial = Trial(beam_list=[Beam(beam_number=1, name="AP")])
            >>> beam = trial.get_beam_by_name("AP")
            >>> print(beam.beam_number)
            1
        """
        for beam in self.beam_list:
            if beam.name == beam_name:
                return beam
        return None

    @property
    def total_monitor_units(self) -> float:
        """
        Calculate the total monitor units for all beams in this trial.

        Returns:
            Sum of monitor units across all beams

        Example:
            >>> trial = Trial(beam_list=[
            ...     Beam(monitor_units=100),
            ...     Beam(monitor_units=200)
            ... ])
            >>> print(trial.total_monitor_units)
            300.0
        """
        pdd = 1.0  # TODO: get pdd from trial
        return sum(beam.compute_monitor_units(pdd) for beam in self.beam_list)

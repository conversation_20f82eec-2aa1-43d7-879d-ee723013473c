"""
Reader for Pinnacle Plan files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Plan, Patient
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader


class PlanReader:
    """
    Reader for Pinnacle Plan files.
    """

    @staticmethod
    def read(patient_path: str, file_service: Any = None) -> List[Plan]:
        """
        Read a Pinnacle Patient file and return the Plan models.
        The patient setup information is also processed and attached to the Plan models.

        Args:
            patient_path: Path to the Pinnacle Patient file or directory
            file_service: File service object with open_file method

        Returns:
            List of Plan models populated with data from the file
        """
        if file_service is not None:
            # New pattern: use file service
            filepath = patient_path
            
            if not file_service.exists(filepath, "Patient"):
                raise FileNotFoundError(f"Patient file not found: {filepath}/Patient")
            
            with file_service.open_file(filepath, "Patient", "r") as f:
                plans = PlanReader.parse(f.readlines())

            # Try to read patient setup for each plan using file service
            for i, plan in enumerate(plans):
                try:
                    plan_dir = os.path.join(filepath, f"Plan_{i}")
                    patient_setup = PatientSetupReader.read(plan_dir, file_service=file_service)
                    plan.set_patient_position(patient_setup)  # type: ignore
                except FileNotFoundError:
                    # Patient setup is optional
                    pass
            return plans
        
        # Backward compatibility: direct file path
        if not patient_path.lower().endswith("patient"):
            file_path = os.path.join(patient_path, "Patient")
        else:
            file_path = patient_path

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Patient file not found: {file_path}")

        with open(file_path, "r", encoding="latin1", errors="ignore") as f:
            plans = PlanReader.parse(f.readlines())

        # Try to read patient setup for each plan
        for i, plan in enumerate(plans):
            try:
                parent_dir = os.path.dirname(file_path) if file_path.lower().endswith("patient") else patient_path
                plan_dir = os.path.join(parent_dir, f"Plan_{i}")
                patient_setup = PatientSetupReader.read(plan_dir)
                plan.set_patient_position(patient_setup)  # type: ignore
            except FileNotFoundError:
                # Patient setup is optional
                pass

        return plans

    @staticmethod
    def parse(content_lines: list[str]) -> List[Plan]:
        """
        Parse a Pinnacle Patient file content and create Plan models.
        The patient setup information is NOT processed by this method.

        Args:
            content_lines: Lines from a Pinnacle Patient file

        Returns:
            List of Plan models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient = Patient(**data)
        return patient.plan_list

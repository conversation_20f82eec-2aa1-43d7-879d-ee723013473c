"""
Tests for the CPManager model.
"""

from pinnacle_io.models import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    CPManager,
)


def test_cp_manager_initialization():
    """Test CPManager initialization with all fields."""
    cp_manager = CPManager(
        is_gantry_start_stop_locked=1,
        is_couch_start_stop_locked=1,
        is_collimator_start_stop_locked=1,
        is_left_right_independent=1,
        is_top_bottom_independent=1,
        gantry_is_ccw=1,
        mlc_push_method="AUTO",
        jaws_conformance="INNER",
        _number_of_control_points=2,
    )

    # Test all fields are set correctly
    assert cp_manager.is_gantry_start_stop_locked == 1
    assert cp_manager.is_couch_start_stop_locked == 1
    assert cp_manager.is_collimator_start_stop_locked == 1
    assert cp_manager.is_left_right_independent == 1
    assert cp_manager.is_top_bottom_independent == 1
    assert cp_manager.gantry_is_ccw == 1
    assert cp_manager.mlc_push_method == "AUTO"
    assert cp_manager.jaws_conformance == "INNER"
    assert cp_manager._number_of_control_points == 2
    assert cp_manager.control_point_list == []


def test_cp_manager_number_of_control_points():
    """Test the number_of_control_points property and setter."""
    cp_manager = CPManager(_number_of_control_points=3)

    # Initially no control points
    assert cp_manager.number_of_control_points == 0

    # Add control points
    ControlPoint(gantry=0.0, cp_manager=cp_manager)
    assert cp_manager.number_of_control_points == 1

    ControlPoint(gantry=180.0, cp_manager=cp_manager)
    assert cp_manager.number_of_control_points == 2

    # Test that _number_of_control_points can be different from actual number
    cp_manager.number_of_control_points = 5
    assert cp_manager._number_of_control_points == 5
    assert cp_manager.number_of_control_points == 2  # Still returns actual count


def test_cp_manager_beam_relationship():
    """Test CPManager relationship with Beam."""
    beam = Beam(name="BEAM1", beam_number=1)
    cp_manager = CPManager(_number_of_control_points=2, beam=beam)

    # Verify beam relationship
    assert cp_manager.beam is beam
    assert cp_manager.beam_id == beam.id
    assert beam.cp_manager is cp_manager

    # Test control points are accessible through beam
    cp1 = ControlPoint(gantry=0.0, beam=beam)
    cp2 = ControlPoint(gantry=90.0, cp_manager=cp_manager)
    cp3 = ControlPoint(gantry=180.0, beam=beam, cp_manager=cp_manager)

    assert cp1 in beam.control_point_list
    assert cp2 in beam.control_point_list
    assert cp3 in beam.control_point_list


def test_cp_manager_repr():
    """Test the __repr__ method of CPManager."""
    cp_manager = CPManager()
    ControlPoint(gantry=0.0, cp_manager=cp_manager)
    ControlPoint(gantry=180.0, cp_manager=cp_manager)

    expected_repr = "<CPManager(id=None, beam='', number_of_control_points=2)>"
    assert repr(cp_manager) == expected_repr

    cp_manager.beam = Beam(name="BEAM1")
    expected_repr = "<CPManager(id=None, beam='BEAM1', number_of_control_points=2)>"
    assert repr(cp_manager) == expected_repr


def test_cp_manager_add_control_point():
    """Test the add_control_point method."""
    import pytest

    cp_manager = CPManager()

    # Test adding a valid control point
    cp1 = ControlPoint(gantry=0.0)
    cp_manager.add_control_point(cp1)

    assert cp1 in cp_manager.control_point_list
    assert cp1.cp_manager is cp_manager
    assert cp_manager.number_of_control_points == 1

    # Test adding another control point
    cp2 = ControlPoint(gantry=90.0)
    cp_manager.add_control_point(cp2)

    assert cp2 in cp_manager.control_point_list
    assert cp2.cp_manager is cp_manager
    assert cp_manager.number_of_control_points == 2

    # Test adding the same control point again (should not duplicate)
    cp_manager.add_control_point(cp1)
    assert cp_manager.number_of_control_points == 2  # Should still be 2

    # Test adding control point that's already associated with another manager
    other_manager = CPManager()
    cp3 = ControlPoint(gantry=180.0, cp_manager=other_manager)

    with pytest.raises(ValueError, match="Control point is already associated with another manager"):
        cp_manager.add_control_point(cp3)

    # Test adding invalid type
    with pytest.raises(TypeError, match="control_point must be a ControlPoint instance"):
        cp_manager.add_control_point("not_a_control_point")


def test_cp_manager_remove_control_point():
    """Test the remove_control_point method."""
    cp_manager = CPManager()

    # Create and add control points
    cp1 = ControlPoint(gantry=0.0)
    cp2 = ControlPoint(gantry=90.0)
    cp3 = ControlPoint(gantry=180.0)

    cp_manager.add_control_point(cp1)
    cp_manager.add_control_point(cp2)
    cp_manager.add_control_point(cp3)

    assert cp_manager.number_of_control_points == 3

    # Test removing a control point
    result = cp_manager.remove_control_point(cp2)
    assert result is True
    assert cp2 not in cp_manager.control_point_list
    assert cp2.cp_manager is None
    assert cp_manager.number_of_control_points == 2

    # Test removing a control point that's not in the list
    cp4 = ControlPoint(gantry=270.0)
    result = cp_manager.remove_control_point(cp4)
    assert result is False
    assert cp_manager.number_of_control_points == 2  # Should remain unchanged

    # Test removing the same control point again
    result = cp_manager.remove_control_point(cp2)
    assert result is False
    assert cp_manager.number_of_control_points == 2


def test_cp_manager_number_of_control_points_setter_validation():
    """Test validation in the number_of_control_points setter."""
    import pytest

    cp_manager = CPManager()

    # Test setting valid values
    cp_manager.number_of_control_points = 5
    assert cp_manager._number_of_control_points == 5

    cp_manager.number_of_control_points = 0
    assert cp_manager._number_of_control_points == 0

    # Test setting invalid values
    with pytest.raises(ValueError, match="Number of control points must be a non-negative integer"):
        cp_manager.number_of_control_points = -1

    with pytest.raises(ValueError, match="Number of control points must be a non-negative integer"):
        cp_manager.number_of_control_points = "invalid"

    with pytest.raises(ValueError, match="Number of control points must be a non-negative integer"):
        cp_manager.number_of_control_points = 3.5


def test_cp_manager_initialization_with_control_points():
    """Test CPManager initialization with control_point_list parameter."""
    # Create control points
    cp1 = ControlPoint(gantry=0.0)
    cp2 = ControlPoint(gantry=90.0)
    cp3 = ControlPoint(gantry=180.0)

    # Test initialization with control point list
    cp_manager = CPManager(control_point_list=[cp1, cp2, cp3], _number_of_control_points=3)

    assert len(cp_manager.control_point_list) == 3
    assert cp1 in cp_manager.control_point_list
    assert cp2 in cp_manager.control_point_list
    assert cp3 in cp_manager.control_point_list
    assert cp_manager.number_of_control_points == 3
    assert cp_manager._number_of_control_points == 3

    # Test initialization with empty list
    cp_manager_empty = CPManager(control_point_list=[])
    assert len(cp_manager_empty.control_point_list) == 0
    assert cp_manager_empty.number_of_control_points == 0

    # Test initialization without control_point_list parameter
    cp_manager_default = CPManager()
    assert len(cp_manager_default.control_point_list) == 0
    assert cp_manager_default.number_of_control_points == 0


def test_cp_manager_edge_cases():
    """Test edge cases and boundary conditions."""
    cp_manager = CPManager()

    # Test number_of_control_points property with empty list
    # (Can't set to None due to SQLAlchemy relationship constraints)
    assert cp_manager.number_of_control_points == 0

    # Test removing control point when list is empty
    cp = ControlPoint(gantry=0.0)
    result = cp_manager.remove_control_point(cp)
    assert result is False

    # Test adding control point that has cp_manager set to self (should work)
    cp1 = ControlPoint(gantry=0.0)
    cp1.cp_manager = cp_manager
    cp_manager.add_control_point(cp1)  # Should not raise error
    assert cp1 in cp_manager.control_point_list

    # Test with very large number of control points
    cp_manager.number_of_control_points = 999999
    assert cp_manager._number_of_control_points == 999999

    # Test repr with None beam
    cp_manager.beam = None
    repr_str = repr(cp_manager)
    assert "beam=''" in repr_str

    # Test repr with beam that has no name attribute (create a real Beam without name)
    beam_no_name = Beam()
    cp_manager.beam = beam_no_name
    repr_str = repr(cp_manager)
    assert "beam=''" in repr_str


def test_cp_manager_complex_scenarios():
    """Test complex scenarios with multiple operations."""
    beam = Beam(name="COMPLEX_BEAM", beam_number=1)
    cp_manager = CPManager(beam=beam, _number_of_control_points=0)

    # Add multiple control points
    control_points = []
    for i in range(5):
        cp = ControlPoint(gantry=i * 72.0)  # 0, 72, 144, 216, 288 degrees
        control_points.append(cp)
        cp_manager.add_control_point(cp)

    assert cp_manager.number_of_control_points == 5
    assert len(cp_manager.control_point_list) == 5

    # Verify all control points are properly associated
    for cp in control_points:
        assert cp.cp_manager is cp_manager
        assert cp in cp_manager.control_point_list

    # Remove some control points
    cp_manager.remove_control_point(control_points[1])  # Remove 72 degrees
    cp_manager.remove_control_point(control_points[3])  # Remove 216 degrees

    assert cp_manager.number_of_control_points == 3
    assert control_points[1] not in cp_manager.control_point_list
    assert control_points[3] not in cp_manager.control_point_list
    assert control_points[0] in cp_manager.control_point_list
    assert control_points[2] in cp_manager.control_point_list
    assert control_points[4] in cp_manager.control_point_list

    # Test that cached number can be different from actual
    cp_manager.number_of_control_points = 10
    assert cp_manager._number_of_control_points == 10
    assert cp_manager.number_of_control_points == 3  # Still returns actual count

    # Verify beam relationship is maintained
    assert cp_manager.beam is beam
    assert beam.cp_manager is cp_manager

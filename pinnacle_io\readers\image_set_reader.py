"""
Reader for Pinnacle ImageSet files.
"""

import os
import numpy as np
from typing import Optional, Any
from pinnacle_io.models import ImageSet
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.image_info_reader import ImageInfoReader


class ImageSetReader:
    """
    Reader for Pinnacle ImageSet files.

    This class provides methods for reading ImageSet files and creating models from the data in the files.
    """

    @staticmethod
    def read(image_path: str, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet file and create an ImageSet model.
        The ImageInfoList is also read from the ImageInfo file.

        Args:
            image_path: Path to ImageSet_# file (either .img or .header extension is required)
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file
        """
        if image_path.lower().endswith(".img"):
            return ImageSetReader.read_image_set(image_path, file_service)

        # If not specified as an img, assume it's a header file
        return ImageSetReader.read_header(image_path, file_service)

    @staticmethod
    def read_header(image_header_path: str, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet header file and create an ImageSet model.
        The ImageInfoList is also read from the ImageInfo file.

        Args:
            image_header_path: Path to ImageSet_# file (the .header extension is optional)
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file
        """
        if file_service is not None:
            # New pattern: use file service
            filepath = image_header_path
            
            if filepath.lower().endswith(".header"):
                # Full path provided, split into directory and filename
                dirname = os.path.dirname(filepath)
                filename = os.path.basename(filepath)
            elif filepath.lower().startswith("imageset_"):
                # Base name provided (ImageSet_0), add extension
                dirname = ""
                filename = filepath + ".header"
            else:
                # Directory path provided, use default filename
                dirname = filepath
                filename = "ImageSet_0.header"
            
            if not file_service.exists(dirname, filename):
                raise FileNotFoundError(f"ImageSet header file not found: {dirname}/{filename}")
            
            with file_service.open_file(dirname, filename, "r") as f:
                image_set = ImageSetReader.parse(f.readlines())
            
            # Read corresponding ImageInfo file
            info_filename = filename.replace(".header", ".ImageInfo")
            image_set.image_info_list = ImageInfoReader.read(os.path.join(dirname, info_filename), file_service)
            return image_set
        
        # Backward compatibility: direct file path
        if not image_header_path.lower().endswith(".header"):
            # If it looks like a base name (ImageSet_0), add the extension
            if not os.path.dirname(image_header_path) or image_header_path.lower().startswith("imageset_"):
                file_path = image_header_path + ".header"
            else:
                # Otherwise assume it's a directory path
                file_path = os.path.join(image_header_path, "ImageSet_0.header")
        else:
            file_path = image_header_path

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"ImageSet header file not found: {file_path}")
        
        with open(file_path, "r", encoding="latin1", errors="ignore") as f:
            image_set = ImageSetReader.parse(f.readlines())

        # Read corresponding ImageInfo file
        info_file_path = file_path.replace(".header", ".ImageInfo")
        image_set.image_info_list = ImageInfoReader.read(info_file_path)
        return image_set

    @staticmethod
    def read_image_set(path: str, file_service: Any = None, image_set: Optional[ImageSet] = None) -> ImageSet:
        """Read a Pinnacle ImageSet file and create an ImageSet model.

        Args:
            path: Path to ImageSet_# file (the .img extension is optional)
            file_service: File service object with open_file method
            image_set: Optional pre-loaded ImageSet model with header information

        Returns:
            ImageSet model populated with data from the file
        """
        if file_service is not None:
            # New pattern: use file service
            filepath = path
            
            if filepath.lower().endswith(".img"):
                # Full path provided, split into directory and filename
                dirname = os.path.dirname(filepath)
                filename = os.path.basename(filepath)
            elif filepath.lower().startswith("imageset_"):
                # Base name provided (ImageSet_0), add extension
                dirname = ""
                filename = filepath + ".img"
            else:
                # Directory path provided, use default filename
                dirname = filepath
                filename = "ImageSet_0.img"
            
            if not file_service.exists(dirname, filename):
                raise FileNotFoundError(f"ImageSet file not found: {dirname}/{filename}")
            
            if image_set is None:
                header_filename = filename.replace(".img", ".header")
                header_path = os.path.join(dirname, header_filename)
                image_set = ImageSetReader.read_header(header_path, file_service)
            
            z_dim = image_set.z_dim if image_set.z_dim is not None else 1
            y_dim = image_set.y_dim if image_set.y_dim is not None else 1
            x_dim = image_set.x_dim if image_set.x_dim is not None else 1
            
            with file_service.open_file(dirname, filename, "rb") as f:
                binary_data = f.read()
                pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)
            
            image_set.pixel_data = pixel_data.tobytes()
            return image_set
        else:
            # Backward compatibility: direct file path
            if not path.lower().endswith(".img"):
                # If it looks like a base name (ImageSet_0), add the extension
                if not os.path.dirname(path) or path.lower().startswith("imageset_"):
                    file_path = path + ".img"
                else:
                    # Otherwise assume it's a directory path
                    file_path = os.path.join(path, "ImageSet_0.img")
            else:
                file_path = path
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"ImageSet file not found: {file_path}")
            
            if image_set is None:
                header_file_path = file_path.replace(".img", ".header")
                image_set = ImageSetReader.read_header(header_file_path)
            
            z_dim = image_set.z_dim if image_set.z_dim is not None else 1
            y_dim = image_set.y_dim if image_set.y_dim is not None else 1
            x_dim = image_set.x_dim if image_set.x_dim is not None else 1
            
            with open(file_path, "rb") as f:
                binary_data = f.read()
                pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)
            
            image_set.pixel_data = pixel_data.tobytes()
            return image_set

    @staticmethod
    def parse(content_lines: list[str]) -> ImageSet:
        """
        Parse a Pinnacle ImageSet header content string and create an ImageSet model.
        The ImageInfoList is not parsed.

        Args:
            content_lines: Pinnacle ImageSet header content lines

        Returns:
            ImageSet model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_set = ImageSet(**data)
        return image_set

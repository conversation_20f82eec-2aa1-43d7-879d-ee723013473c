"""
Reader for Pinnacle plan.roi files.
"""

import numpy as np
import os
from typing import List, Any
from pinnacle_io.models import RO<PERSON>, Curve
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class ROIReader:
    """
    Reader for Pinnacle plan.roi files.
    """

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> List[ROI]:
        """
        Read a Pinnacle plan.roi file and create a list of ROI models.

        Args:
            plan_path: Path to the patient's plan directory or plan.roi file
            file_service: File service object with open_file method

        Returns:
            List of ROI models populated with data from the file
        """
        if plan_path.lower().endswith("plan.roi"):
            file_path, file_name = os.path.split(plan_path)
        else:
            file_path, file_name = plan_path, "plan.roi"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"plan.roi file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return ROIReader.parse(f.readlines())
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"plan.roi file not found: {full_path}")
        
        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return ROIReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> List[ROI]:
        """
        Parse lines from a Pinnacle plan.roi file into a list of ROI models.

        Args:
            content_lines: List of lines from the ROI file.

        Returns:
            List of ROI models populated with data from the content.
        """
        # Instead of evaluating the entire file line by line,
        #
        lines = [line.strip() for line in content_lines]
        beginning_of_rois = [i for i in range(len(lines)) if lines[i] == "roi={"]
        beginning_of_curves = [i for i in range(len(lines)) if lines[i] == "curve={"]

        rois: list[ROI] = []
        i_roi = 0
        i_curve = 0
        while i_roi < len(beginning_of_rois) and i_curve < len(beginning_of_curves):
            beginning_of_roi = beginning_of_rois[i_roi]
            beginning_of_curve = beginning_of_curves[i_curve]
            roi_lines = lines[beginning_of_roi + 1 : beginning_of_curve]
            roi_data = PinnacleFileReader.parse(roi_lines)
            roi_data["roi_number"] = i_roi + 1
            roi = ROI(**roi_data)

            curve_number = 0
            while curve_number < roi_data["num_curve"]:
                beginning_of_curve = beginning_of_curves[i_curve]
                curve_lines = lines[beginning_of_curve + 1 : beginning_of_curve + 4]
                curve_data = PinnacleFileReader.parse(curve_lines)
                curve_data["curve_number"] = curve_number

                beginning_of_points = beginning_of_curve + 5
                point_lines = lines[beginning_of_points : beginning_of_points + curve_data["num_points"]]
                curve_data["points"] = np.array([list(map(float, line.split())) for line in point_lines])
                roi.curve_list.append(Curve(**curve_data))

                curve_number += 1
                i_curve += 1

            rois.append(roi)
            i_roi += 1

        return rois

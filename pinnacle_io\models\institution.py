"""
SQLAlchemy model for Pinnacle Institution data.
"""

from sqlalchemy import Column, String, Integer
from sqlalchemy.orm import Mapped, relationship
from typing import Any

from pinnacle_io.models.versioned_base import VersionedBase

from pinnacle_io.models.patient import Patient
from pinnacle_io.models.patient_lite import PatientLite


class Institution(VersionedBase):
    """
    Model representing a Pinnacle Institution in the radiation therapy treatment planning system.

    This class serves as the top-level organizational entity in the Pinnacle system,
    representing a medical institution, hospital department, or clinic that uses Pinnacle
    for radiation therapy treatment planning. It acts as the root container for all
    patient data, treatment plans, physics configurations, and institutional settings
    within a single organizational unit.

    The Institution model manages the complete data hierarchy including patient records,
    backup configurations, storage requirements, and system-level settings. It provides
    the organizational structure needed for multi-institutional deployments and ensures
    proper data isolation and management across different medical facilities.

    Technical Details:
        - Inherits from VersionedBase for audit trail and versioning support
        - Supports hierarchical data organization with cascade delete operations
        - Manages both lightweight (PatientLite) and full Patient records
        - Handles backup and restore operations for institutional data
        - Tracks storage space requirements across different data categories
        - Supports multiple backup device types and formats
        - Provides dynamic patient loading for large datasets

    Data Management Features:
        - Automatic cascade deletion of all child records when institution is deleted
        - Dynamic loading of patient lists for memory efficiency
        - Comprehensive backup and restore functionality
        - Storage space monitoring and management
        - Support for legacy data format migrations
        - Cross-platform file path handling

    Use Cases:
        - Multi-site radiation therapy networks
        - Hospital department data organization
        - Backup and disaster recovery operations
        - Data migration between Pinnacle versions
        - Storage capacity planning and monitoring
        - Institutional reporting and analytics

    Attributes:
        # Core Identification
        id (int): Primary key identifier
        institution_id (int): Unique institution identifier from Pinnacle system
        name (str): Human-readable institution name

        # File System and Paths
        institution_path (str): Primary filesystem path to institution data directory
        pinn_institution_path (str): Pinnacle-specific path for internal references
        default_mount_point (str): Default mount point for data storage operations

        # Address and Contact Information
        street_address (str): Primary street address of the institution
        street_address_2 (str): Secondary address line (suite, building, etc.)
        city (str): City name
        state (str): State, province, or region
        zip_code (str): Postal or ZIP code
        country (str): Country name

        # Storage Space Requirements (in bytes)
        device_space_required_patients (int): Space needed for patient data
        device_space_required_physics (int): Space needed for physics/machine data
        device_space_required_scripts (int): Space needed for custom scripts
        device_space_required_organ_models (int): Space needed for organ model data
        device_space_required_atlas (int): Space needed for anatomical atlas data

        # Backup Configuration and Metadata
        backup_description (str): Human-readable backup description
        backup_volume (str): Volume identifier for backup storage
        backup_file_name (str): Filename for backup archives
        backup_time_stamp (str): Timestamp of last backup operation
        backup_device_type (str): Type of backup device ("TAPE", "DISK", "NETWORK")
        backup_id (int): Unique identifier for backup operations
        session (str): Session identifier for backup/restore operations

        # Directory Paths for Specialized Data
        scripts_dir (str): Directory path for custom scripts
        organ_models_dir (str): Directory path for organ model files
        atlas_file (str): File path for anatomical atlas data

        # Backup Operation Flags (0/1 boolean values)
        is_patient_backup (int): Flag indicating if patient data is included in backup
        is_physics_machines_backup (int): Flag for physics/machine data backup inclusion
        include_physics_data (int): Flag to include physics data in operations
        include_all_patients (int): Flag to include all patients in backup
        machines_in_v7_format (int): Flag indicating machine data format version
        is_solaris_format (int): Flag for Solaris-specific data format compatibility
        full_file_name_included (int): Flag for full path inclusion in backup
        dynamic_rebuild (int): Flag enabling dynamic data rebuilding

    Relationships:
        patient_lite_list (list[PatientLite]): Lightweight patient records for quick access
            - Cascade: all, delete-orphan (automatic cleanup)
            - Use for: Patient listings, searches, summary views

        patient_list (list[Patient]): Complete patient records with full treatment data
            - Cascade: all, delete-orphan (automatic cleanup)
            - Lazy: dynamic (loaded on-demand for memory efficiency)
            - Use for: Full patient data access, treatment planning

    Example:
        >>> # Create a new institution
        >>> institution = Institution(
        ...     name="City General Hospital - Radiation Oncology",
        ...     street_address="123 Medical Center Drive",
        ...     city="Springfield",
        ...     state="IL",
        ...     zip_code="62701",
        ...     country="USA",
        ...     institution_path="/data/institutions/city_general"
        ... )
        >>>
        >>> # Access patient data
        >>> print(f"Institution: {institution.name}")
        >>> print(f"Total patients: {len(institution.patient_lite_list)}")
        >>>
        >>> # Configure backup settings
        >>> institution.backup_device_type = "NETWORK"
        >>> institution.include_all_patients = 1
        >>> institution.is_patient_backup = 1
    """

    __tablename__: str = "Institution"

    # Primary key is inherited from PinnacleBase
    institution_id: Mapped[int | None] = Column("InstitutionID", Integer)
    institution_path: Mapped[str | None] = Column("InstitutionPath", String, nullable=True)
    pinn_institution_path: Mapped[str | None] = Column("PinnInstitutionPath", String, nullable=True)
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    street_address: Mapped[str | None] = Column("StreetAddress", String, nullable=True)
    street_address_2: Mapped[str | None] = Column("StreetAddress2", String, nullable=True)
    city: Mapped[str | None] = Column("City", String, nullable=True)
    state: Mapped[str | None] = Column("State", String, nullable=True)
    zip_code: Mapped[str | None] = Column("ZipCode", String, nullable=True)
    country: Mapped[str | None] = Column("Country", String, nullable=True)

    # Device space requirements
    device_space_required_patients: Mapped[int | None] = Column("DeviceSpaceRequiredPatients", Integer, nullable=True)
    device_space_required_physics: Mapped[int | None] = Column("DeviceSpaceRequiredPhysics", Integer, nullable=True)
    device_space_required_scripts: Mapped[int | None] = Column("DeviceSpaceRequiredScripts", Integer, nullable=True)
    device_space_required_organ_models: Mapped[int | None] = Column("DeviceSpaceRequiredOrganModels", Integer, nullable=True)
    device_space_required_atlas: Mapped[int | None] = Column("DeviceSpaceRequiredAtlas", Integer, nullable=True)
    # Backup information
    default_mount_point: Mapped[str | None] = Column("DefaultMountPoint", String, nullable=True)
    backup_description: Mapped[str | None] = Column("BackupDescription", String, nullable=True)
    backup_volume: Mapped[str | None] = Column("BackupVolume", String, nullable=True)
    backup_file_name: Mapped[str | None] = Column("BackupFileName", String, nullable=True)
    session: Mapped[str | None] = Column("Session", String, nullable=True)
    scripts_dir: Mapped[str | None] = Column("ScriptsDir", String, nullable=True)
    organ_models_dir: Mapped[str | None] = Column("OrganModelsDir", String, nullable=True)
    atlas_file: Mapped[str | None] = Column("AtlasFile", String, nullable=True)
    backup_time_stamp: Mapped[str | None] = Column("BackupTimeStamp", String, nullable=True)
    backup_device_type: Mapped[str | None] = Column("BackupDeviceType", String, nullable=True)

    # Backup flags
    is_patient_backup: Mapped[int | None] = Column("IsPatientBackup", Integer, nullable=True)
    is_physics_machines_backup: Mapped[int | None] = Column("IsPhysicsMachinesBackup", Integer, nullable=True)
    include_physics_data: Mapped[int | None] = Column("IncludePhysicsData", Integer, nullable=True)
    machines_in_v7_format: Mapped[int | None] = Column("MachinesInV7Format", Integer, nullable=True)
    is_solaris_format: Mapped[int | None] = Column("IsSolarisFormat", Integer, nullable=True)
    include_all_patients: Mapped[int | None] = Column("IncludeAllPatients", Integer, nullable=True)
    full_file_name_included: Mapped[int | None] = Column("FullFileNameIncluded", Integer, nullable=True)
    backup_id: Mapped[int | None] = Column("BackupID", Integer, nullable=True)
    dynamic_rebuild: Mapped[int | None] = Column("DynamicRebuild", Integer, nullable=True)

    # Child Relationships
    patient_lite_list: Mapped[list["PatientLite"]] = relationship(
        "PatientLite",
        back_populates="institution",
        cascade="all, delete-orphan",
        lazy="selectin",  # Optimize loading for collections that are frequently accessed
    )
    patient_list: Mapped[list["Patient"]] = relationship(
        "Patient",
        back_populates="institution",
        cascade="all, delete-orphan",
        lazy="select",  # Load on demand as patient lists can be large
    )

    def __init__(self, **kwargs: Any) -> None:
        """Initialize an Institution instance.

        Args:
            **kwargs: Keyword arguments used to initialize Institution attributes.
                     Valid attributes include all column names and relationship names.

        Example:
            >>> inst = Institution(
            ...     name="Example Medical Center",
            ...     street_address="123 Main St",
            ...     city="Anytown",
            ...     state="CA",
            ...     zip_code="12345",
            ...     country="USA"
            ... )
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """
        Return a string representation of this institution.
        """
        return f"<Institution(id={self.id}, institution_id={self.institution_id}, name='{self.name}')>"

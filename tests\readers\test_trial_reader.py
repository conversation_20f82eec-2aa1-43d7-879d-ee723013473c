"""
Tests for the TrialReader (extracted from test_trial.py).
"""

from pathlib import Path
import pytest

from pinnacle_io.readers.trial_reader import TrialReader
from pinnacle_io.models import Trial
from pinnacle_io.services.file_reader import FileReader


def test_read_trial_file():
    """Tests reading a valid Trial file using direct path (backward compatibility)."""
    plan_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    trials = TrialReader.read(plan_path=str(plan_path))

    # Verify we got trials back
    assert len(trials) > 0
    trial = trials[0]  # Get first trial

    # Basic trial properties
    assert isinstance(trial, Trial)
    assert trial.name == "Trial_1"

    # Check beam list
    assert len(trial.beam_list) == 2
    beam = trial.beam_list[0]
    assert beam.name == "02 Lao Brain"
    assert beam.modality == "Photons"
    assert beam.machine_energy_name == "6X"

    # Check prescription
    assert len(trial.prescription_list) == 1
    prescription = trial.prescription_list[0]
    assert prescription.name == "Brain"
    assert prescription.prescription_dose == 250
    assert prescription.number_of_fractions == 12
    assert prescription.prescription_point == "iso"

    # Check dose grid settings
    assert trial.dose_grid.voxel_size.x == 0.3
    assert trial.dose_grid.voxel_size.y == 0.3
    assert trial.dose_grid.voxel_size.z == 0.3
    assert trial.dose_grid.dimension.x == 93
    assert trial.dose_grid.dimension.y == 110
    assert trial.dose_grid.dimension.z == 89


def test_read_trial_file_with_service():
    """Tests reading a valid Trial file using file service."""
    plan_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    file_service = FileReader(str(plan_path))
    trials = TrialReader.read(file_service=file_service)

    # Verify we got trials back
    assert len(trials) > 0
    trial = trials[0]  # Get first trial

    # Basic trial properties
    assert isinstance(trial, Trial)
    assert trial.name == "Trial_1"

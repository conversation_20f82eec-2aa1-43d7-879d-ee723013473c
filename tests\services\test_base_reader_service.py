import pytest
from abc import ABC
from pinnacle_io.services.base_reader_service import BaseReaderService


class MockReaderService(BaseReaderService):
    """Mock implementation of BaseReaderService for testing."""

    def __init__(self, files=None):
        self.files = files or {}

    def open_file(self, filepath, filename=None, mode="r"):
        # Handle both old and new calling conventions for backwards compatibility
        if filename is None:
            # Old style: filepath is actually the filename
            full_path = filepath
        else:
            # New style: combine filepath and filename
            full_path = f"{filepath}/{filename}" if filepath else filename
        
        if full_path not in self.files:
            raise FileNotFoundError(f"File {full_path} not found")
        content = self.files[full_path]
        if "b" in mode:
            return MockBinaryFile(content.encode() if isinstance(content, str) else content)
        return MockTextFile(content)

    def exists(self, filepath, filename=None):
        # Handle both old and new calling conventions for backwards compatibility
        if filename is None:
            # Old style: filepath is actually the filename
            full_path = filepath
        else:
            # New style: combine filepath and filename
            full_path = f"{filepath}/{filename}" if filepath else filename
        
        return full_path in self.files


class MockTextFile:
    """Mock text file object."""

    def __init__(self, content):
        self.content = content
        self.lines = content.splitlines(keepends=True) if content else []

    def readlines(self):
        return self.lines

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


class MockBinaryFile:
    """Mock binary file object."""

    def __init__(self, content):
        self.content = content

    def read(self):
        return self.content

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


class TestBaseReaderService:
    """Test cases for BaseReaderService abstract base class."""

    def test_is_abstract_class(self):
        """Test that BaseReaderService is an abstract class."""
        assert issubclass(BaseReaderService, ABC)

    def test_cannot_instantiate_directly(self):
        """Test that BaseReaderService cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseReaderService()

    def test_abstract_methods_exist(self):
        """Test that abstract methods are defined."""
        assert hasattr(BaseReaderService, 'open_file')
        assert hasattr(BaseReaderService, 'exists')

    def test_concrete_methods_exist(self):
        """Test that concrete get_* methods are defined."""
        methods = [
            'get_institution', 'get_patient', 'get_image_set', 'get_image_info',
            'get_trials', 'get_rois', 'get_points', 'get_patient_setup',
            'get_machines', 'get_dose'
        ]
        for method in methods:
            assert hasattr(BaseReaderService, method)

    def test_get_institution(self):
        """Test get_institution method with mock data."""
        institution_content = 'Name = "Test Institution";\nAddress = "123 Test St";\n'
        service = MockReaderService({'Institution': institution_content})
        
        # This would normally return an Institution object, but we're testing the file access
        # The actual parsing is tested in the reader tests
        institution = service.get_institution()
        assert institution is not None

    def test_get_patient(self):
        """Test get_patient method with mock data."""
        patient_content = 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n'
        service = MockReaderService({'Patient': patient_content})
        
        patient = service.get_patient()
        assert patient is not None

    def test_get_image_set(self):
        """Test get_image_set method with mock data."""
        image_set_content = 'VoxelSize = { x = 1.0; y = 1.0; z = 1.0; };\n'
        service = MockReaderService({'ImageSet_0.header': image_set_content})
        
        image_set = service.get_image_set(0)
        assert image_set is not None

    def test_get_image_info(self):
        """Test get_image_info method with mock data."""
        image_info_content = 'Modality = "CT";\nSliceThickness = 2.5;\n'
        service = MockReaderService({'ImageSet_0.ImageInfo': image_info_content})
        
        image_info = service.get_image_info(0)
        assert image_info is not None

    def test_get_trials(self):
        """Test get_trials method with mock data."""
        trial_content = 'Trial = {\n  Name = "Test Trial";\n};\n'
        service = MockReaderService({'plan.Trial': trial_content})
        
        trials = service.get_trials()
        assert trials is not None

    def test_get_rois(self):
        """Test get_rois method with mock data."""
        roi_content = 'ROI = {\n  Name = "Test ROI";\n};\n'
        service = MockReaderService({'plan.roi': roi_content})
        
        rois = service.get_rois()
        assert rois is not None

    def test_get_points(self):
        """Test get_points method with mock data."""
        points_content = 'PoiList = {\n  Point = {\n  Name = "Test Point";\n};\n};\n'
        service = MockReaderService({'plan.Points': points_content})
        
        points = service.get_points()
        assert points is not None

    def test_get_patient_setup(self):
        """Test get_patient_setup method with mock data."""
        setup_content = 'PatientSetup = {\n  Position = "HFS";\n};\n'
        service = MockReaderService({'plan.PatientSetup': setup_content})
        
        setup = service.get_patient_setup()
        assert setup is not None

    def test_get_machines(self):
        """Test get_machines method with mock data."""
        machines_content = 'Machine = {\n  Name = "Test Machine";\n};\n'
        service = MockReaderService({'plan.Pinnacle.Machines': machines_content})
        
        machines = service.get_machines()
        assert machines is not None

    def test_file_not_found_error(self):
        """Test that FileNotFoundError is raised when file doesn't exist."""
        service = MockReaderService({})
        
        with pytest.raises(FileNotFoundError):
            service.get_institution()

    def test_exists_method(self):
        """Test the exists method."""
        service = MockReaderService({'test_file': 'content'})
        
        assert service.exists('test_file') is True
        assert service.exists('nonexistent_file') is False
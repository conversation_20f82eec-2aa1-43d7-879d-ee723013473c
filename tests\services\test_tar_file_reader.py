import pytest
import tarfile
import tempfile
import os
from pathlib import Path
from pinnacle_io.services.tar_file_reader import TarFileReader


class TestTarFileReader:
    """Test cases for TarFileReader service."""

    @pytest.fixture
    def test_tar_path(self):
        """Get path to test tar file."""
        test_dir = Path(__file__).parent.parent
        return test_dir / "test_data" / "01.tar.gz"

    @pytest.fixture
    def tar_reader(self, test_tar_path):
        """Create TarFileReader instance with test data."""
        if test_tar_path.exists():
            return TarFileReader(str(test_tar_path))
        else:
            pytest.skip(f"Test tar file not found: {test_tar_path}")

    @pytest.fixture
    def temp_tar_file(self):
        """Create a temporary tar file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as temp_tar:
            temp_tar_path = temp_tar.name
        
        try:
            # Create tar file with test content
            with tarfile.open(temp_tar_path, 'w:gz') as tar:
                # Add test files with different path formats
                test_files = {
                    'Institution': 'Name = "Test Institution";\nAddress = "123 Test St";\n',
                    'Patient': 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n',
                    'plan.Trial': 'Trial = {\n  Name = "Test Trial";\n};\n',
                    'Institution_1/Mount_0/Patient_1/Patient': 'Name = "Nested Patient";\n',
                    'Institution_1/Mount_0/Patient_1/Plan_0/plan.roi': 'ROI = {\n  Name = "Test ROI";\n};\n',
                    # Test with Linux-style paths (common in tar files)
                    'linux/style/path.txt': 'Linux style path content\n',
                    # Test with special characters
                    'special-file_name.txt': 'Special filename content\n'
                }
                
                for filename, content in test_files.items():
                    # Create tarinfo
                    tarinfo = tarfile.TarInfo(name=filename)
                    tarinfo.size = len(content.encode())
                    
                    # Add to tar
                    import io
                    tar.addfile(tarinfo, io.BytesIO(content.encode()))
            
            yield temp_tar_path, test_files
        finally:
            # Clean up with retry logic for Windows
            self._cleanup_temp_file(temp_tar_path)
    
    def _cleanup_temp_file(self, temp_path):
        """Helper method to clean up temporary files with retry logic for Windows."""
        import time
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                break
            except PermissionError:
                if attempt < max_retries - 1:
                    time.sleep(0.1)  # Brief wait before retry
                else:
                    # If we can't delete, just log it - don't fail the test
                    pass

    def test_init(self, temp_tar_file):
        """Test TarFileReader initialization."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        assert reader.tar_path == temp_tar_path
        assert reader._tarfile is not None

    def test_open_file_existing(self, temp_tar_file):
        """Test opening existing files from tar."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']

    def test_open_file_binary_mode(self, temp_tar_file):
        """Test opening files in binary mode."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read().replace(b'\r\n', b'\n')
            assert content == test_files['Institution'].encode()

    def test_open_file_not_found(self, temp_tar_file):
        """Test FileNotFoundError when file doesn't exist in tar."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'nonexistent_file.txt', 'r')
        
        assert "nonexistent_file.txt not found in tar archive" in str(exc_info.value)

    def test_open_file_nested_path(self, temp_tar_file):
        """Test opening files in nested directories within tar."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        nested_file = 'Institution_1/Mount_0/Patient_1/Patient'
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files[nested_file]

    def test_exists_file_found(self, temp_tar_file):
        """Test exists method returns True for existing files."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        assert reader.exists('', 'Institution') is True
        assert reader.exists('', 'Patient') is True
        assert reader.exists('', 'plan.Trial') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True

    def test_exists_file_not_found(self, temp_tar_file):
        """Test exists method returns False for non-existing files."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        assert reader.exists('', 'nonexistent_file.txt') is False
        assert reader.exists('Institution_1/Mount_0', 'nonexistent.txt') is False

    def test_linux_path_format_in_tar(self, temp_tar_file):
        """Test with Linux-style paths (common in tar files)."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Tar files typically use Linux-style paths regardless of host OS
        linux_path = 'linux/style/path.txt'
        assert reader.exists(linux_path) is True
        
        with reader.open_file('linux/style', 'path.txt', 'r') as f:
            content = f.read()
            assert content == test_files[linux_path]

    def test_windows_to_linux_path_conversion(self, temp_tar_file):
        """Test that Windows-style paths are handled appropriately."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Note: In tar files, paths are always stored with forward slashes
        # Windows-style backslashes would be part of the filename itself
        # So this test confirms that Windows-style paths don't match Linux-style ones
        nested_path_linux = 'Institution_1/Mount_0/Patient_1/Patient'
        nested_path_windows = 'Institution_1\\Mount_0\\Patient_1\\Patient'
        
        assert reader.exists(nested_path_linux) is True
        # This should be False because tar stores paths with forward slashes
        assert reader.exists(nested_path_windows) is False

    def test_special_filenames(self, temp_tar_file):
        """Test files with special characters in names."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        special_file = 'special-file_name.txt'
        assert reader.exists('', special_file) is True
        
        with reader.open_file('', special_file, 'r') as f:
            content = f.read()
            assert content == test_files[special_file]

    def test_integration_with_real_test_data(self, tar_reader):
        """Test with actual test tar file if available."""
        if tar_reader is None:
            pytest.skip("Real test tar file not available")
        
        # Test that we can check for existence of files that might be in the real tar
        potential_files = [
            '01/Institution',
            '01/Institution_1/Mount_0/Patient_1/Patient',
            '01/Institution_1/Mount_0/Patient_1/ImageSet_0.header',
            '01/Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial'
        ]
        
        for filename in potential_files:
            if tar_reader.exists(filename):
                # If file exists, we should be able to open it
                with tar_reader.open_file(filename, mode='r') as f:
                    content = f.read()
                    assert isinstance(content, str)
                    assert len(content) > 0

    def test_error_message_includes_tar_path(self, temp_tar_file):
        """Test that error messages include helpful tar path information."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'missing_file.txt', 'r')
        
        error_msg = str(exc_info.value)
        assert 'missing_file.txt' in error_msg
        assert 'tar archive' in error_msg

    def test_file_modes(self, temp_tar_file):
        """Test different file opening modes."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test text mode
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert isinstance(content, str)
        
        # Test binary mode
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read()
            assert isinstance(content, bytes)

    def test_text_wrapper_encoding(self, temp_tar_file):
        """Test that text mode uses latin1 encoding as specified."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        with reader.open_file('', 'Institution', 'r') as f:
            # Check that we have a TextIOWrapper
            assert hasattr(f, 'encoding')
            # The encoding should be latin1 as specified in the implementation
            assert f.encoding == 'latin1'

    def test_tar_file_cleanup(self, temp_tar_file):
        """Test that tar file resources are properly managed."""
        temp_tar_path, _ = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Access the internal tar file
        tar_file = reader._tarfile
        assert tar_file is not None
        
        # Use the reader
        assert reader.exists('', 'Institution') is True
        
        # The tar file should still be accessible
        assert not tar_file.closed

    def test_nonexistent_tar_file(self):
        """Test behavior when tar file doesn't exist."""
        with pytest.raises((FileNotFoundError, tarfile.ReadError)):
            TarFileReader('/nonexistent/path/to/file.tar.gz')

    def test_invalid_tar_file(self):
        """Test behavior with invalid tar file."""
        # Create a non-tar file
        with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as temp_file:
            temp_file.write(b'This is not a tar file')
            temp_path = temp_file.name
        
        try:
            with pytest.raises(tarfile.ReadError):
                TarFileReader(temp_path)
        finally:
            self._cleanup_temp_file(temp_path)

    def test_new_api_filepath_filename(self, temp_tar_file):
        """Test the new API with separate filepath and filename parameters."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test with separate filepath and filename
        assert reader.exists('', 'Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_new_api_full_path(self, temp_tar_file):
        """Test the new API with full path as filepath parameter."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test with full path as filepath (filename=None)
        assert reader.exists('Institution') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Patient') is True
        
        with reader.open_file('Institution', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Patient', mode='r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']

    def test_pattern_matching(self, temp_tar_file):
        """Test that the tar reader can find files using pattern matching."""
        temp_tar_path, test_files = temp_tar_file
        reader = TarFileReader(temp_tar_path)
        
        # Test pattern matching - should find files that start with filepath and end with filename
        assert reader.exists('Institution_1/Mount_0/Patient_1', 'Patient') is True
        assert reader.exists('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi') is True
        
        with reader.open_file('Institution_1/Mount_0/Patient_1', 'Patient', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Patient']
        
        with reader.open_file('Institution_1/Mount_0/Patient_1/Plan_0', 'plan.roi', 'r') as f:
            content = f.read()
            assert content == test_files['Institution_1/Mount_0/Patient_1/Plan_0/plan.roi']
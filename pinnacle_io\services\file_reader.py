from .base_reader_service import BaseReaderService
from typing import Any, IO


class FileReader(BaseReaderService):
    """
    FileReader service for reading Pinnacle data from a directory on the filesystem.
    Implements open_file using Python's built-in open().
    All get_* methods are inherited from BaseReaderService.
    """

    def __init__(self, root_path: str):
        self.root_path = root_path

    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file from the filesystem relative to the root_path.
        Args:
            filepath: Directory path or full file path within the root directory.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode (e.g., 'r', 'rb').
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the filesystem.
        """
        import os

        if filename is None:
            full_path = os.path.join(self.root_path, filepath) if filepath else self.root_path
        else:
            full_path = os.path.join(self.root_path, filepath, filename)
        
        if not os.path.exists(full_path):
            display_path = os.path.join(filepath, filename) if filename else filepath
            raise FileNotFoundError(f"File {display_path} not found in directory {self.root_path}")
        return open(full_path, mode)

    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the filesystem relative to the root_path.
        Args:
            filepath: Directory path or full file path within the root directory.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        import os

        if filename is None:
            full_path = os.path.join(self.root_path, filepath) if filepath else self.root_path
        else:
            full_path = os.path.join(self.root_path, filepath, filename)
        
        return os.path.exists(full_path)

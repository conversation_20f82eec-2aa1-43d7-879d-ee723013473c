"""
SQLAlchemy model for Pinnacle Prescription data.

This module provides the Prescription model for representing treatment prescription
details within a Pinnacle trial. It includes dose targets, fractionation schemes,
and normalization parameters.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any

from sqlalchemy import <PERSON>umn, <PERSON>, Integer, Float, <PERSON>Key
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.trial import Trial


class Prescription(PinnacleBase):
    """
    Model representing a radiation therapy prescription within a Pinnacle treatment trial.

    This class stores comprehensive prescription information that defines the therapeutic
    intent and dose delivery parameters for radiation therapy treatment. Prescriptions
    are fundamental components of treatment planning that specify the total dose,
    fractionation schedule, normalization methods, and quality assurance parameters
    required for safe and effective radiation therapy delivery.

    In Pinnacle, a trial can contain multiple prescriptions to accommodate complex
    treatment scenarios such as sequential boost treatments, simultaneous integrated
    boosts (SIB), or multi-phase treatment protocols. Each prescription defines specific
    dose objectives and constraints that guide treatment planning optimization and
    delivery verification.

    Technical Details:
        - Supports multiple normalization methods (point-based, isodose-based, maximum dose)
        - Handles uncertainty analysis for dose delivery validation
        - Integrates with monitor unit calculations for treatment delivery
        - Provides color coding for visual identification in treatment planning
        - Supports complex fractionation schemes and dose escalation protocols

    Clinical Applications:
        - Primary target volume prescriptions
        - Boost volume prescriptions for dose escalation
        - Simultaneous integrated boost (SIB) treatments
        - Sequential treatment phases
        - Adaptive radiation therapy protocols
        - Stereotactic treatments with high precision requirements

    Use Cases:
        - Treatment plan optimization and evaluation
        - Dose-volume histogram (DVH) analysis
        - Monitor unit calculation and verification
        - Treatment delivery quality assurance
        - Clinical protocol compliance verification
        - Multi-institutional treatment standardization

    Attributes:
        # Core Identification
        id (int): Primary key identifier for the prescription
        name (str): Human-readable prescription name (e.g., "PTV_70Gy", "Boost_Phase_II")
        color (str): Color code for visual identification in treatment planning interface

        # Dose Specification
        prescription_dose (float): Total prescribed dose in Gray (Gy)
        number_of_fractions (int): Number of treatment fractions for dose delivery
        prescription_percent (int): Isodose line percentage for dose normalization (e.g., 95%)
        prescription_point (str): Point of Interest (POI) name used for dose prescription

        # Normalization and Calculation Methods
        normalization_method (str): Dose normalization method specification
            - "MaxDose": Normalize to maximum dose in target
            - "ToPoint": Normalize to specific prescription point
            - "MeanDose": Normalize to mean target dose
            - "MedianDose": Normalize to median target dose
        method (str): Prescription calculation method
        prescription_period (str): Treatment period specification for fractionation
        weights_proportional_to (str): Beam weight proportionality method

        # Monitor Unit and Delivery Parameters
        requested_monitor_units_per_fraction (int): Target monitor units per fraction

        # Uncertainty Analysis and Quality Assurance
        dose_uncertainty (int): Dose calculation uncertainty percentage
        prescription_uncertainty (int): Prescription delivery uncertainty percentage
        dose_uncertainty_valid (int): Flag indicating dose uncertainty validity (0/1)
        prescrip_uncertainty_valid (int): Flag indicating prescription uncertainty validity (0/1)

        # Parent Relationship
        trial_id (int): Foreign key to the parent Trial

    Relationships:
        trial (Trial): Parent treatment trial that contains this prescription (many-to-one)
            - Back reference: trial.prescription_list
            - Lazy loading: joined (optimized for frequent access)
            - Use for: Treatment planning context and multi-prescription coordination

    Example:
        >>> # Create primary prescription for prostate treatment
        >>> primary_rx = Prescription(
        ...     name="PTV_Prostate_78Gy",
        ...     prescription_dose=78.0,
        ...     number_of_fractions=39,
        ...     prescription_percent=95,
        ...     prescription_point="Isocenter",
        ...     normalization_method="ToPoint",
        ...     color="Red"
        ... )
        >>>
        >>> # Create boost prescription for SIB treatment
        >>> boost_rx = Prescription(
        ...     name="PTV_Boost_84Gy",
        ...     prescription_dose=84.0,
        ...     number_of_fractions=28,
        ...     prescription_percent=98,
        ...     normalization_method="MeanDose",
        ...     color="Blue"
        ... )
        >>>
        >>> # Calculate dose per fraction
        >>> dose_per_fx = primary_rx.prescription_dose / primary_rx.number_of_fractions
        >>> print(f"Dose per fraction: {dose_per_fx:.2f} Gy")
    """

    __tablename__: str = "Prescription"

    # Primary key is inherited from PinnacleBase
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    requested_monitor_units_per_fraction: Mapped[int | None] = Column("RequestedMonitorUnitsPerFraction", Integer, nullable=True)
    prescription_dose: Mapped[float | None] = Column("PrescriptionDose", Float, nullable=True)
    prescription_percent: Mapped[int | None] = Column("PrescriptionPercent", Integer, nullable=True)
    number_of_fractions: Mapped[int | None] = Column("NumberOfFractions", Integer, nullable=True)
    prescription_point: Mapped[str | None] = Column("PrescriptionPoint", String, nullable=True)
    method: Mapped[str | None] = Column("Method", String, nullable=True)
    normalization_method: Mapped[str | None] = Column("NormalizationMethod", String, nullable=True)
    prescription_period: Mapped[str | None] = Column("PrescriptionPeriod", String, nullable=True)
    weights_proportional_to: Mapped[str | None] = Column("WeightsProportionalTo", String, nullable=True)
    dose_uncertainty: Mapped[int | None] = Column("DoseUncertainty", Integer, nullable=True)
    prescription_uncertainty: Mapped[int | None] = Column("PrescriptionUncertainty", Integer, nullable=True)
    dose_uncertainty_valid: Mapped[int | None] = Column("DoseUncertaintyValid", Integer, nullable=True)
    prescrip_uncertainty_valid: Mapped[int | None] = Column("PrescripUncertaintyValid", Integer, nullable=True)
    color: Mapped[str | None] = Column("Color", String, nullable=True)

    # Foreign key to the parent Trial
    trial_id: Mapped[int] = Column("TrialID", Integer, ForeignKey("Trial.ID"), nullable=False)

    # Parent relationship, eagerly loaded for performance.
    trial: Mapped["Trial"] = relationship("Trial", back_populates="prescription_list", lazy="joined")

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a Prescription instance.

        Args:
            **kwargs: Keyword arguments for model attributes.
                Common attributes include:
                - name: Name of the prescription
                - prescription_dose: Prescribed dose value
                - number_of_fractions: Number of treatment fractions
                - trial_id: ID of the parent trial

        Relationships:
            trial (Trial): The parent Trial to which this prescription belongs (many-to-one).
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """
        Provide a developer-friendly string representation of the Prescription.
        """
        return f"<Prescription(id={self.id}, name='{self.name}', dose={self.prescription_dose}, fx={self.number_of_fractions})>"

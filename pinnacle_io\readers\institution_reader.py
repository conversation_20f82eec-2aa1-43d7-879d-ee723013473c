"""
Reader for Pinnacle Institution files.
"""

import os
from typing import Any
from pinnacle_io.models import Institution
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class InstitutionReader:
    """
    Reader for Pinnacle Institution files.
    """

    @staticmethod
    def read(institution_path: str | None = None, file_service: Any = None) -> Institution:
        """
        Read a Pinnacle Institution file and create an Institution model.

        Args:
            institution_path: Path to the Institution file or directory
            file_service: File service object with open_file method

        Returns:
            Institution model populated with data from the files
        """
        institution_path = institution_path or ""
        
        if institution_path.endswith("Institution"):
            file_path, file_name = os.path.split(institution_path)
        else:
            file_path, file_name = institution_path, "Institution"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"Institution file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return InstitutionReader.parse(f.readlines())
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Institution file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return InstitutionReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> Institution:
        """
        Parse a Pinnacle Institution content string and create an Institution model.

        Args:
            content_lines: Pinnacle Institution content lines

        Returns:
            Institution model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        institution = Institution(**data)
        return institution

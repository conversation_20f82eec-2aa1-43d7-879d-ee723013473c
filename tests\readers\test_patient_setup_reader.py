"""
Tests for the PatientSetupReader class.
"""

from pathlib import Path
from pinnacle_io.models import PatientSetup
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader
from pinnacle_io.services.file_reader import FileReader


def test_read_patient_setup_file():
    """Tests reading a valid plan.PatientSetup file using direct path (backward compatibility)."""
    # Adjust the path as needed to point to a real test file
    setup_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    patient_setup = PatientSetupReader.read(plan_path=str(setup_path))

    assert isinstance(patient_setup, PatientSetup)
    # Check values from the test file
    assert patient_setup.position == "On back (supine)"
    assert patient_setup.orientation == "Head First Into Scanner"
    assert patient_setup.table_motion == "Table Moves Into Scanner"
    assert patient_setup.create_version == "Pinnacle v16.0"
    assert patient_setup.create_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert patient_setup.write_version == "Pinnacle v16.0"
    assert patient_setup.write_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"


def test_read_patient_setup_file_with_service():
    """Tests reading a valid plan.PatientSetup file using file service."""
    setup_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    file_service = FileReader(str(setup_path))
    patient_setup = PatientSetupReader.read(file_service=file_service)

    assert isinstance(patient_setup, PatientSetup)
    # Check values from the test file
    assert patient_setup.position == "On back (supine)"
    assert patient_setup.orientation == "Head First Into Scanner"

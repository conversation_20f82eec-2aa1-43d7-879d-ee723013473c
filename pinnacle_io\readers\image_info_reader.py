"""
Reader for Pinnacle ImageInfo files (extracted from ImageSetReader).
"""

import os
from typing import Any
from pinnacle_io.models import ImageInfo
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class ImageInfoReader:
    """
    Reader for Pinnacle ImageSet_#.ImageInfo files.
    """

    @staticmethod
    def read(image_info_path: str, file_service: Any = None) -> list[ImageInfo]:
        """
        Read a Pinnacle ImageSet info file and create a list of ImageInfo models.

        Args:
            image_info_path: Path to ImageSet_# file or directory (the .ImageInfo extension is optional)
            file_service: File service object with open_file method

        Returns:
            List of ImageInfo models populated with data from the file
        """
        if file_service is not None:
            # New pattern: use file service
            filepath = image_info_path
            
            if filepath.lower().endswith(".imageinfo"):
                # Full path provided, split into directory and filename
                dirname = os.path.dirname(filepath)
                filename = os.path.basename(filepath)
            elif filepath.lower().startswith("imageset_"):
                # Base name provided (ImageSet_0), add extension
                dirname = ""
                filename = filepath + ".ImageInfo"
            else:
                # Directory path provided, use default filename
                dirname = filepath
                filename = "ImageSet_0.ImageInfo"
            
            if not file_service.exists(dirname, filename):
                raise FileNotFoundError(f"ImageSet info file not found: {dirname}/{filename}")
            
            with file_service.open_file(dirname, filename, "r") as f:
                return ImageInfoReader.parse(f.readlines())
        
        # Backward compatibility: direct file path
        if not image_info_path.lower().endswith(".imageinfo"):
            # If it looks like a base name (ImageSet_0), add the extension
            if not os.path.dirname(image_info_path) or image_info_path.lower().startswith("imageset_"):
                file_path = image_info_path + ".ImageInfo"
            else:
                # Otherwise assume it's a directory path
                file_path = os.path.join(image_info_path, "ImageSet_0.ImageInfo")
        else:
            file_path = image_info_path

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"ImageSet info file not found: {file_path}")
        
        with open(file_path, "r", encoding="latin1", errors="ignore") as f:
            return ImageInfoReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> list[ImageInfo]:
        """
        Parse a Pinnacle ImageSet info content string and create a list of ImageInfo models.

        Args:
            content_lines: Pinnacle ImageSet info content lines

        Returns:
            List of ImageInfo models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_info_list = [ImageInfo(**image_info) for image_info in data.get("ImageInfoList", {})]
        return image_info_list

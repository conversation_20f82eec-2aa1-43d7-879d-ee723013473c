import pytest
import os
import tempfile
from pathlib import Path
from pinnacle_io.services.file_reader import FileReader


class TestFileReader:
    """Test cases for FileReader service."""

    @pytest.fixture
    def test_data_path(self):
        """Get path to test data folder."""
        test_dir = Path(__file__).parent.parent
        return test_dir / "test_data" / "01"

    @pytest.fixture
    def file_reader(self, test_data_path):
        """Create FileReader instance with test data."""
        return FileReader(str(test_data_path))

    @pytest.fixture
    def temp_test_files(self):
        """Create temporary test files for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files with different content
            files = {
                'Institution': 'Name = "Test Institution";\nAddress = "123 Test St";\n',
                'Patient': 'Name = "Test Patient";\nMedicalRecordNumber = "12345";\n',
                'plan.Trial': 'Trial = {\n  Name = "Test Trial";\n};\n',
                'subdir/nested_file.txt': 'Nested file content\n'
            }
            
            # Create subdirectory
            os.makedirs(os.path.join(temp_dir, 'subdir'), exist_ok=True)
            
            # Write test files
            for filename, content in files.items():
                file_path = os.path.join(temp_dir, filename)
                with open(file_path, 'w') as f:
                    f.write(content)
            
            yield temp_dir, files

    def test_init(self, test_data_path):
        """Test FileReader initialization."""
        reader = FileReader(str(test_data_path))
        assert reader.root_path == str(test_data_path)

    def test_open_file_existing(self, temp_test_files):
        """Test opening existing files."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == files['Institution']

    def test_open_file_binary_mode(self, temp_test_files):
        """Test opening files in binary mode."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read().replace(b'\r\n', b'\n')
            assert content == files['Institution'].encode()

    def test_open_file_not_found(self, temp_test_files):
        """Test FileNotFoundError when file doesn't exist."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('', 'nonexistent_file.txt', 'r')
        
        assert "nonexistent_file.txt not found in directory" in str(exc_info.value)

    def test_open_file_nested_path(self, temp_test_files):
        """Test opening files in nested directories."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        with reader.open_file('subdir', 'nested_file.txt', 'r') as f:
            content = f.read()
            assert content == files['subdir/nested_file.txt']

    def test_exists_file_found(self, temp_test_files):
        """Test exists method returns True for existing files."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        assert reader.exists('', 'Institution') is True
        assert reader.exists('', 'Patient') is True
        assert reader.exists('', 'plan.Trial') is True
        assert reader.exists('subdir', 'nested_file.txt') is True

    def test_exists_file_not_found(self, temp_test_files):
        """Test exists method returns False for non-existing files."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        assert reader.exists('', 'nonexistent_file.txt') is False
        assert reader.exists('subdir', 'nonexistent.txt') is False

    def test_windows_path_format(self, temp_test_files):
        """Test with Windows-style path separators."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        # Test with Windows-style path separator
        # With the new API, we can test Windows-style paths by using filepath parameter
        if os.name == 'nt':
            assert reader.exists('subdir\\nested_file.txt') is True
            with reader.open_file('subdir\\nested_file.txt', mode='r') as f:
                content = f.read()
                assert 'Nested file content' in content

    def test_linux_path_format(self, temp_test_files):
        """Test with Linux-style path separators."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        # Test with Linux-style path separator (should work on all platforms)
        assert reader.exists('subdir/nested_file.txt') is True
        with reader.open_file('subdir/nested_file.txt', mode='r') as f:
            content = f.read()
            assert 'Nested file content' in content

    def test_path_normalization(self, temp_test_files):
        """Test that different path formats point to the same file."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        # These should all point to the same file
        paths = [
            'subdir/nested_file.txt',
            './subdir/nested_file.txt',
        ]
        
        if os.name == 'nt':
            paths.append('subdir\\nested_file.txt')
        
        for path in paths:
            if reader.exists(path):
                with reader.open_file(path, mode='r') as f:
                    content = f.read()
                    assert 'Nested file content' in content

    def test_integration_with_real_test_data(self, file_reader):
        """Test with actual test data files."""
        # Test that we can check for existence of real test files
        test_files = [
            'Institution',
            'Institution_1/Mount_0/Patient_1/Patient',
            'Institution_1/Mount_0/Patient_1/ImageSet_0.header',
            'Institution_1/Mount_0/Patient_1/Plan_0/plan.Trial'
        ]
        
        for filename in test_files:
            if file_reader.exists(filename):
                # If file exists, we should be able to open it
                with file_reader.open_file(filename, mode='r') as f:
                    content = f.read()
                    assert isinstance(content, str)
                    assert len(content) > 0

    def test_error_message_includes_path(self, temp_test_files):
        """Test that error messages include helpful path information."""
        temp_dir, _ = temp_test_files
        reader = FileReader(temp_dir)
        
        with pytest.raises(FileNotFoundError) as exc_info:
            reader.open_file('missing_file.txt', mode='r')
        
        error_msg = str(exc_info.value)
        assert 'missing_file.txt' in error_msg
        assert temp_dir in error_msg

    def test_file_modes(self, temp_test_files):
        """Test different file opening modes."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        # Test text mode
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert isinstance(content, str)
        
        # Test binary mode
        with reader.open_file('', 'Institution', 'rb') as f:
            content = f.read()
            assert isinstance(content, bytes)
        
        # Test write mode (if supported by open_file)
        try:
            with reader.open_file('', 'test_write.txt', 'w') as f:
                f.write('test content')
            
            # Verify we can read it back
            with reader.open_file('', 'test_write.txt', 'r') as f:
                content = f.read()
                assert content == 'test content'
        except (OSError, IOError):
            # Write mode might not be supported, that's okay
            pass

    def test_empty_root_path(self):
        """Test FileReader with empty root path."""
        reader = FileReader("")
        
        # Should work with current directory
        assert reader.root_path == ""
        
        # Test that exists works (though files may not exist)
        exists_result = reader.exists('', 'nonexistent.txt')
        assert isinstance(exists_result, bool)

    def test_new_api_filepath_filename(self, temp_test_files):
        """Test the new API with separate filepath and filename parameters."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        # Test with separate filepath and filename
        assert reader.exists('', 'Institution') is True
        assert reader.exists('subdir', 'nested_file.txt') is True
        
        with reader.open_file('', 'Institution', 'r') as f:
            content = f.read()
            assert content == files['Institution']
        
        with reader.open_file('subdir', 'nested_file.txt', 'r') as f:
            content = f.read()
            assert content == files['subdir/nested_file.txt']

    def test_new_api_full_path(self, temp_test_files):
        """Test the new API with full path as filepath parameter."""
        temp_dir, files = temp_test_files
        reader = FileReader(temp_dir)
        
        # Test with full path as filepath (filename=None)
        assert reader.exists('Institution') is True
        assert reader.exists('subdir/nested_file.txt') is True
        
        with reader.open_file('Institution', mode='r') as f:
            content = f.read()
            assert content == files['Institution']
        
        with reader.open_file('subdir/nested_file.txt', mode='r') as f:
            content = f.read()
            assert content == files['subdir/nested_file.txt']
"""
SQLAlchemy model for Pinnacle WedgeContext data.

This module provides the WedgeContext data model for representing beam wedge configurations
in Pinnacle, including physical properties, geometric parameters, and beam modifier settings
needed for treatment planning and DICOM conversion.
"""

from __future__ import annotations
from typing import TYPE_CHECKING, Any

from sqlalchemy import <PERSON>umn, Integer, Float, String, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.control_point import ControlPoint


class WedgeContext(PinnacleBase):
    """
    Model representing a treatment beam wedge in Pinnacle.

    This class stores all wedge-specific information including physical properties,
    geometric parameters, and machine-specific settings. It plays a crucial role in both
    treatment planning and DICOM export by defining beam modifying devices used for
    creating desired dose distributions.

    The wedge parameters encompass both physical wedges (STANDARD type) and dynamic wedges,
    allowing for precise control over beam modification and dose distribution.

    Attributes:
        id (int): Primary key for database identification
        wedge_id (str): Unique identifier for the wedge device
        wedge_number (int): Sequential number for the wedge in the treatment system
        wedge_angle (float): Angular measure of the wedge in degrees
        wedge_type (str, optional): Type of wedge (e.g., "STANDARD", "DYNAMIC")
        wedge_orientation (str, optional): Orientation axis ("X" or "Y")
        wedge_position (str, optional): Position state ("IN" or "OUT")
        material (str, optional): Physical material of the wedge
        source_to_wedge_distance (float, optional): Distance from source to wedge in mm
        wedge_name (str, optional): Descriptive name of the wedge
        orientation (str, optional): Directional orientation (e.g., "LEFT", "RIGHT")
        offset_origin (str, optional): Reference point for offset measurements
        offset_distance (float, optional): Distance from the offset origin
        angle (str, optional): String representation of the wedge angle
        min_deliverable_mu (int, optional): Minimum deliverable Monitor Units
        max_deliverable_mu (float, optional): Maximum deliverable Monitor Units

    Relationships:
        control_point (ControlPoint): Parent control point that this wedge belongs to
    """

    __tablename__: str = "WedgeContext"

    # Required fields
    wedge_id: Mapped[str] = Column("WedgeID", String, nullable=False)
    wedge_number: Mapped[int] = Column("WedgeNumber", Integer, nullable=False)
    wedge_angle: Mapped[float] = Column("WedgeAngle", Float, nullable=False)

    # Optional fields
    wedge_type: Mapped[str | None] = Column("WedgeType", String, nullable=True)
    wedge_orientation: Mapped[str | None] = Column("WedgeOrientation", String, nullable=True)
    wedge_position: Mapped[str | None] = Column("WedgePosition", String, nullable=True)

    # Physical properties
    material: Mapped[str | None] = Column("Material", String, nullable=True)
    source_to_wedge_distance: Mapped[float | None] = Column("SourceToWedgeDistance", Float, nullable=True)

    # Additional properties
    wedge_name: Mapped[str | None] = Column("WedgeName", String, nullable=True)
    orientation: Mapped[str | None] = Column("Orientation", String, nullable=True)
    offset_origin: Mapped[str | None] = Column("OffsetOrigin", String, nullable=True)
    offset_distance: Mapped[float | None] = Column("OffsetDistance", Float, nullable=True)
    angle: Mapped[str | None] = Column("Angle", String, nullable=True)
    min_deliverable_mu: Mapped[int | None] = Column("MinDeliverableMU", Integer, nullable=True)
    max_deliverable_mu: Mapped[float | None] = Column("MaxDeliverableMU", Float, nullable=True)

    # Parent relationship
    control_point_id: Mapped[int | None] = Column(
        "ControlPointID",
        Integer,
        ForeignKey("ControlPoint.ID", ondelete="CASCADE"),
        nullable=True,
    )
    control_point: Mapped["ControlPoint | None"] = relationship(
        "ControlPoint",
        back_populates="wedge_context",
        lazy="joined",  # Optimize loading as wedge data is frequently needed with control point
        single_parent=True,
    )

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize a WedgeContext instance.

        Args:
            **kwargs: Keyword arguments used to initialize WedgeContext attributes.
                     See class attributes for available fields.
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """Return a string representation of the WedgeContext."""
        return f"<WedgeContext(id={self.id}, name='{self.wedge_name}', angle='{self.angle}', orientation='{self.orientation}')>"

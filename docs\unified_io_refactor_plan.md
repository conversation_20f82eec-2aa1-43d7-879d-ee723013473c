# Unified IO Refactor Plan for pinnacle_io

## Overview

This plan describes the steps to implement a unified, extensible IO interface for the `pinnacle_io` package, enabling simple and advanced file access for Pinnacle data. The new interface will support reading from directories, tar files, zip files, and in-memory archives, and will be easily extensible for future IO types and writing capabilities.

**Note:** All new service classes, including the reader service abstraction, will be placed in a new `pinnacle_io/services` folder.

---

## Supported Data Objects and File Mappings

| File Name Pattern                  | Data Object         | Method to Implement in BaseReaderService |
|------------------------------------|---------------------|------------------------------------------|
| Institution                        | Institution         | get_institution()                        |
| Patient                            | Patient             | get_patient()                            |
| ImageSet_#.header / ImageSet_#.img | ImageSet            | get_image_set()                          |
| ImageSet_#.ImageInfo               | ImageInfo           | get_image_info()                         |
| plan.Trial                         | List[Trial]         | get_trials()                             |
| plan.roi                           | List[ROI]           | get_rois()                               |
| plan.Points                        | List[Point]         | get_points()                             |
| plan.PatientSetup                  | PatientSetup        | get_patient_setup()                      |
| plan.Pinnacle.Machines             | List[Machine]       | get_machines()                           |
| plan.Trial.binary.###              | Dose                | get_dose()                               |

---

## Step-by-Step Implementation Checklist

### 1. Design the Reader Service Abstraction

- [x] Create a new `pinnacle_io/services` folder to contain the new service classes.
- [x] Define `BaseReaderService` (abstract base class) in `pinnacle_io/services/base_reader_service.py`.
  - Abstract methods for: `get_institution`, `get_patient`, `get_image_set`, `get_image_info`, `get_trials`, `get_rois`, `get_points`, `get_patient_setup`, `get_machines`, `get_dose`
  - Abstract file access method: `open_file(filename)` (returns file-like object)
  - Support both file path and in-memory archive sources
  - **Status:** Implemented as `BaseReaderService` in `pinnacle_io/services/base_reader_service.py`.

### 2. Implement IO Service Subclasses

- [x] Implement `FileReader` (directory-based) in `pinnacle_io/services/file_reader.py`
  - Inherits from `BaseReaderService`
  - Implements file access using the filesystem
  - **Status:** Implemented as `FileReader` in `pinnacle_io/services/file_reader.py`.
- [x] Implement `TarFileReader` in `pinnacle_io/services/tar_file_reader.py`
  - Inherits from `BaseReaderService`
  - Uses `tarfile` module for file access
  - Support both file path and in-memory tar archives
  - **Status:** Implemented as `TarFileReader` in `pinnacle_io/services/tar_file_reader.py`.
- [x] Implement `ZipFileReader` in `pinnacle_io/services/zip_file_reader.py`
  - Inherits from `BaseReaderService`
  - Uses `zipfile` module for file access
  - Support both file path and in-memory zip archives
  - **Status:** Implemented as `ZipFileReader` in `pinnacle_io/services/zip_file_reader.py`.

### 3. Refactor Existing Reader Classes

- [x] Update all reader classes (e.g., `InstitutionReader`)
  - Accept a file-like object or a file accessor service, not just a file path
  - Refactor so they can work with any source (directory, tar, zip, memory)
  - Provide backward compatibility for direct file path usage

### 4. Inject Reader Service into All Reader Classes

- [x] Update each reader class in `pinnacle_io.readers` to accept and use the reader service for file access:
  - [x] `InstitutionReader`
  - [x] `PatientReader`
  - [x] `ImageSetReader`
  - [x] `ImageInfoReader`
  - [x] `TrialReader`
  - [x] `ROIReader`
  - [x] `PointReader`
  - [x] `PlanReader`
  - [x] `PatientSetupReader`
  - [x] `MachineReader`
  - [x] `DoseReader`

### 5. Implement Top-Level API Functions

- [ ] Implement `pinnacle_io.read(path)`
  - Detects file type/source (directory, tar, zip, memory) and instantiates the appropriate reader
  - Returns the data object corresponding to the file name (see mapping above)
- [ ] Implement `pinnacle_io.write(path, obj)`
  - Stub for now: raise NotImplementedError for tar/zip/in-memory writes
  - Implement for directory-based writes if feasible

### 6. Expose New Classes and Functions in `pinnacle_io/__init__.py`

- [ ] Add imports and `__all__` entries for new classes and functions

### 7. Unit Testing with Pytest

- [ ] Write tests for `BaseReaderService` (using a mock or dummy subclass)
- [ ] Write tests for `FileReader` (using temporary directories/files)
- [ ] Write tests for `TarFileReader` (using in-memory or temp tar files)
- [ ] Write tests for `ZipFileReader` (using in-memory or temp zip files)
- [ ] Write tests for top-level `read` and `write` functions
- [ ] Test integration with existing reader classes (ensure they work with all IO services)
- [ ] Test error handling (missing files, corrupt archives, unsupported file types)
- [ ] Test round-trip read/write for directory-based IO

### 8. Documentation and Examples

- [ ] Update or add docstrings for all new classes and methods
- [ ] Add usage examples to the README or a new `docs/usage.md`

---

## Optional/Recommended Enhancements (for Future Work)

- Pluggable IO backends (e.g., S3, HTTP)
- Caching layer for performance
- Thread safety for parallel access
- Type annotations and static analysis (mypy/pyright)
- Full write support for tar/zip/in-memory

---

## Notes

- For now, writing to tar/zip/in-memory is not implemented; raise `NotImplementedError` in those cases.
- All IO services should support both file paths and in-memory archives.
- The top-level `read` function should infer the data object type from the file name and return the appropriate object.
- Focus on correctness and extensibility; performance and concurrency can be addressed later.

---

## Summary Table

| Step | Description                        | Status |
|------|------------------------------------|--------|
| 1    | Design `BaseReaderService`         | [x]    |
| 2    | Implement IO subclasses            | [x]    |
| 3    | Refactor existing readers          | [x]    |
| 4    | Inject reader service into readers | [x]    |
| 5    | Implement top-level API            | [ ]    |
| 6    | Expose in `__init__.py`            | [ ]    |
| 7    | Pytest unit tests                  | [x]    |
| 8    | Documentation/examples             | [ ]    |

--- 
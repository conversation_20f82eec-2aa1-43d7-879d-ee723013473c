"""
SQLAlchemy models for Pinnacle machine configuration data.

This module provides data models for machine configurations in Pinnacle,
including rotational beam delivery settings and tolerance tables.
"""

from __future__ import annotations
from typing import Any, TYPE_CHECKING

from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, Inte<PERSON>, ForeignKey
from sqlalchemy.orm import Mapped, relationship

from pinnacle_io.models.pinnacle_base import PinnacleBase

# Use TYPE_CHECKING to avoid circular imports
if TYPE_CHECKING:
    from pinnacle_io.models.machine import Machine


class ConfigRV(PinnacleBase):
    """
    Model representing Record and Verify (R&V) configuration for Pinnacle treatment machines.

    This class stores comprehensive machine-specific parameters for Record and Verify systems,
    which are critical safety components in radiation therapy that monitor and verify treatment
    delivery parameters in real-time. The R&V system ensures that the actual treatment delivery
    matches the planned parameters within acceptable tolerances.

    The ConfigRV model manages the configuration of jaw positions, MLC settings, and vendor-specific
    parameters that define how the R&V system interprets and validates treatment delivery data.
    This configuration is essential for ensuring patient safety and treatment accuracy during
    radiation therapy delivery.

    Technical Details:
        - Supports vendor-specific configurations (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.)
        - Manages jaw position validation parameters
        - Handles MLC bank and order swap configurations
        - Provides real-time treatment monitoring capabilities
        - Ensures compliance with safety protocols and standards

    Use Cases:
        - Treatment delivery verification and monitoring
        - Real-time safety checks during beam delivery
        - Vendor-specific machine integration
        - Quality assurance and compliance reporting
        - Treatment parameter validation and tolerance checking

    Attributes:
        # Core Configuration
        id (int): Primary key identifier
        enabled (int): Flag indicating if R&V configuration is active (1=enabled, 0=disabled)

        # Jaw Position Configuration
        left_jaw (str): Left jaw position validation parameters and settings
        right_jaw (str): Right jaw position validation parameters and settings
        top_jaw (str): Top jaw position validation parameters and settings
        bottom_jaw (str): Bottom jaw position validation parameters and settings

        # MLC Configuration Parameters
        mlc_bank_swap (str): MLC bank swap configuration for proper leaf identification
        mlc_order_swap (str): MLC leaf order swap settings for vendor compatibility

        # Vendor-Specific Settings
        elekta_relative_mlc_positions (int): Elekta-specific MLC position interpretation flag (1=relative, 0=absolute)

        # Parent Relationship
        machine_id (int): Foreign key to the parent Machine

    Relationships:
        machine (Machine): The parent treatment machine that owns this R&V configuration (one-to-one)
            - Back reference: machine.config_rv
            - Lazy loading: selectin (optimized loading)
            - Use for: Machine-specific R&V parameter access

    Example:
        >>> # Create R&V configuration for a Varian machine
        >>> config = ConfigRV(
        ...     enabled=1,
        ...     left_jaw="VALIDATE_POSITION",
        ...     right_jaw="VALIDATE_POSITION",
        ...     top_jaw="VALIDATE_POSITION",
        ...     bottom_jaw="VALIDATE_POSITION",
        ...     mlc_bank_swap="STANDARD",
        ...     mlc_order_swap="NONE"
        ... )
        >>>
        >>> # Configure for Elekta machine with relative MLC positions
        >>> elekta_config = ConfigRV(
        ...     enabled=1,
        ...     elekta_relative_mlc_positions=1,
        ...     mlc_bank_swap="ELEKTA_STANDARD"
        ... )
    """

    __tablename__: str = "ConfigRV"

    # Configuration parameters
    enabled: Mapped[int | None] = Column("Enabled", Integer, nullable=True)
    left_jaw: Mapped[str | None] = Column("LeftJaw", String, nullable=True)
    right_jaw: Mapped[str | None] = Column("RightJaw", String, nullable=True)
    top_jaw: Mapped[str | None] = Column("TopJaw", String, nullable=True)
    bottom_jaw: Mapped[str | None] = Column("BottomJaw", String, nullable=True)
    mlc_bank_swap: Mapped[str | None] = Column("MLCBankSwap", String, nullable=True)
    mlc_order_swap: Mapped[str | None] = Column("MLCOrderSwap", String, nullable=True)
    elekta_relative_mlc_positions: Mapped[int | None] = Column("ElektaRelativeMLCPositions", Integer, nullable=True)

    # Relationships
    machine_id: Mapped[int] = Column(Integer, ForeignKey("Machine.ID"))
    machine: Mapped["Machine"] = relationship(
        "Machine",
        back_populates="config_rv",
        lazy="joined",  # Optimize loading as machine info is frequently needed
    )

    def __repr__(self) -> str:
        return (
            f"<ConfigRV(id={self.id}, "
            f"machine_id={self.machine_id}, "
            f"enabled={self.enabled}, "
            f"left_jaw='{self.left_jaw}', "
            f"right_jaw='{self.right_jaw}', "
            f"top_jaw='{self.top_jaw}', "
            f"bottom_jaw='{self.bottom_jaw}')>"
        )

    def __init__(self, **kwargs: Any):
        """
        Initialize a ConfigRV instance.

        Args:
            **kwargs: Arbitrary keyword arguments used to initialize ConfigRV attributes.
        """
        super().__init__(**kwargs)


class TolTable(PinnacleBase):
    """
    Model representing tolerance tables for Pinnacle treatment machine quality assurance.

    This class stores comprehensive tolerance settings that define the acceptable variations
    in beam delivery parameters for radiation therapy quality assurance and safety monitoring.
    Tolerance tables are critical safety components that establish the permissible deviations
    from planned treatment parameters during beam delivery, ensuring patient safety and
    treatment accuracy.

    The TolTable model manages machine-specific tolerance configurations that are used by
    Record and Verify (R&V) systems to validate treatment delivery in real-time. These
    tolerances cover various treatment parameters including gantry angles, collimator positions,
    jaw settings, MLC positions, dose rates, and monitor units.

    Technical Details:
        - Supports multiple tolerance sets per machine
        - Integrates with R&V systems for real-time monitoring
        - Provides configurable tolerance levels for different treatment types
        - Supports vendor-specific tolerance requirements
        - Enables automated treatment interruption on tolerance violations

    Clinical Applications:
        - IMRT/VMAT treatment delivery monitoring
        - Stereotactic radiosurgery precision verification
        - Conventional treatment parameter validation
        - Machine commissioning and acceptance testing
        - Ongoing quality assurance programs

    Use Cases:
        - Real-time treatment delivery monitoring
        - Quality assurance protocol implementation
        - Machine-specific safety parameter configuration
        - Regulatory compliance and documentation
        - Treatment technique-specific tolerance management

    Attributes:
        # Core Identification
        id (int): Primary key identifier
        name (str): Human-readable name of the tolerance table (e.g., "IMRT_Tight", "SRS_Ultra_Precise")
        number (int): Numerical identifier for the tolerance table within the machine configuration

        # Parent Relationship
        machine_id (int): Foreign key to the parent Machine

    Relationships:
        machine (Machine): The parent treatment machine that owns this tolerance table (many-to-one)
            - Back reference: machine.tolerance_table_list
            - Lazy loading: selectin (optimized loading)
            - Use for: Machine-specific tolerance parameter access

    Example:
        >>> # Create tolerance table for IMRT treatments
        >>> imrt_tol = TolTable(
        ...     name="IMRT_Standard",
        ...     number=1
        ... )
        >>>
        >>> # Create high-precision tolerance table for SRS
        >>> srs_tol = TolTable(
        ...     name="SRS_High_Precision",
        ...     number=2
        ... )
        >>>
        >>> # Access tolerance tables for a machine
        >>> machine = session.query(Machine).first()
        >>> for tol_table in machine.tolerance_table_list:
        ...     print(f"Tolerance Table: {tol_table.name} (#{tol_table.number})")
    """

    __tablename__: str = "TolTable"

    # Tolerance table attributes
    name: Mapped[str | None] = Column("Name", String, nullable=True)
    number: Mapped[int | None] = Column("Number", Integer, nullable=True)

    # Relationships
    machine_id: Mapped[int] = Column(Integer, ForeignKey("Machine.ID"))
    machine: Mapped["Machine"] = relationship(
        "Machine",
        back_populates="tolerance_table_list",
        lazy="joined",  # Optimize loading as machine info is frequently needed
    )

    def __repr__(self) -> str:
        return f"<TolTable(id={self.id}, name='{self.name}', number={self.number})>"

    def __init__(self, **kwargs: Any):
        """
        Initialize a TolTable instance.

        Args:
            **kwargs: Arbitrary keyword arguments used to initialize TolTable attributes.
        """
        super().__init__(**kwargs)

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pinnacle_io"
version = "0.1.0"
description = "A Python library for reading and writing Pinnacle radiotherapy treatment planning files"
readme = "README.md"
requires-python = ">=3.9"
license = {file = "LICENSE"}
authors = [
    {name = "pinnacle_io Contributors"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Healthcare Industry",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
]
dependencies = [
    "sqlalchemy>=2.0.0",
    "pydantic>=2.0.0",
    "numpy>=1.21.0",
]

[project.optional-dependencies]
gui = [
    "PyQt6>=6.0.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

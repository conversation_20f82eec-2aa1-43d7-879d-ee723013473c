"""
Tests for the ImageSetReader class (extracted from test_image_set.py and test_image_info.py).
"""

from pathlib import Path
from pinnacle_io.models import ImageSet, ImageInfo
from pinnacle_io.readers.image_set_reader import ImageSetReader
from pinnacle_io.services.file_reader import FileReader
import io
import numpy as np
import pytest
from unittest import mock

TEST_DATA_DIR = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"


def test_read_image_set_file():
    """Tests reading a valid ImageSet file using direct path (backward compatibility)."""
    image_set = ImageSetReader.read_header(str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/ImageSet_0"))

    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.study_uid is None
    assert image_set.series_description == "HEAD"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512
    assert image_set.y_dim == 512
    assert image_set.z_dim == 101
    assert image_set.x_pixdim == 0.097656
    assert image_set.y_pixdim == 0.097656
    assert image_set.z_pixdim == 0.25

    assert len(image_set.image_info_list) > 2
    assert image_set.image_info_list[0].table_position == -8.75
    assert image_set.image_info_list[0].slice_number == 1
    assert image_set.image_info_list[0].dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.1.dcm"
    assert image_set.image_info_list[1].table_position == -8.5
    assert image_set.image_info_list[1].slice_number == 2
    assert image_set.image_info_list[1].dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.2.dcm"


def test_read_image_set_img_file():
    """Tests reading a valid ImageSet .img file (pixel data)."""
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    image_set = ImageSetReader.read(str(img_path))
    assert isinstance(image_set, ImageSet)
    assert hasattr(image_set, "pixel_data")
    # Pixel data should be bytes and correct length
    z, y, x = image_set.z_dim, image_set.y_dim, image_set.x_dim
    assert image_set.pixel_data is not None
    arr = np.frombuffer(image_set.pixel_data, dtype=np.uint16)
    assert arr.size == z * y * x


def test_read_image_set_header_file():
    """Tests reading a valid ImageSet .header file via read()."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_set = ImageSetReader.read(str(header_path))
    assert isinstance(image_set, ImageSet)
    assert image_set.series_description == "HEAD"


def test_read_image_set_invalid_extension():
    """Tests read() with an invalid file extension raises ValueError."""
    with pytest.raises(ValueError):
        ImageSetReader.read(str(TEST_DATA_DIR / "ImageSet_0.invalid"))


def test_read_header_missing_file():
    """Tests read_header() with a missing file raises FileNotFoundError."""
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_header(str(TEST_DATA_DIR / "does_not_exist"))


def test_read_image_set_missing_img():
    """Tests read_image_set() with a missing .img file raises FileNotFoundError."""
    with pytest.raises(FileNotFoundError):
        ImageSetReader.read_image_set(str(TEST_DATA_DIR / "does_not_exist.img"))


def test_read_image_set_with_provided_image_set():
    """Tests read_image_set() with a provided image_set argument."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    image_set = ImageSetReader.read_header(str(header_path))
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    result = ImageSetReader.read_image_set(str(img_path), image_set=image_set)
    assert result is image_set
    assert hasattr(result, "pixel_data")


def test_parse_valid_content():
    """Tests parse() with valid header content."""
    header_path = TEST_DATA_DIR / "ImageSet_0.header"
    with open(header_path, "r", encoding="latin1", errors="ignore") as f:
        lines = f.readlines()
    image_set = ImageSetReader.parse(lines)
    assert isinstance(image_set, ImageSet)
    assert image_set.series_description == "HEAD"


def test_read_image_set_defaults_for_missing_dims():
    """Tests read_image_set() defaults to 1 for missing x/y/z dims."""
    # Mock ImageSet with None for dims
    dummy = ImageSet(x_dim=None, y_dim=None, z_dim=None)
    img_path = TEST_DATA_DIR / "ImageSet_0.img"
    # Patch open to return enough bytes for 1*1*1 uint16
    with mock.patch("builtins.open", mock.mock_open(read_data=(1).to_bytes(2, "little"))):
        result = ImageSetReader.read_image_set(str(img_path), image_set=dummy)
        arr = np.frombuffer(result.pixel_data, dtype=np.uint16)
        assert arr.size == 1


def test_read_image_set_file_with_service():
    """Tests reading a valid ImageSet file using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    file_service = FileReader(str(test_data_dir))
    image_set = ImageSetReader.read(file_service=file_service, index=0)

    assert isinstance(image_set, ImageSet)
    assert image_set.series_uid == "1.2.840.113619.2.55.3.**********.000.**********.000"
    assert image_set.modality == "CT"
    assert image_set.x_dim == 512
